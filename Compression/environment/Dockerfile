# hash:sha256:9f0c0accda7264625ec19c6c279bb6752dbb48bf0a522d410b7f8da43090fffd
FROM registry.codeocean.com/codeocean/pytorch:2.1.0-cuda11.8.0-mambaforge23.1.0-4-python3.10.12-ubuntu22.04

ARG DEBIAN_FRONTEND=noninteractive

RUN pip3 install -U --no-cache-dir \
    accelerate==0.27.2 \
    bitsandbytes==0.42.0 \
    numpy==1.26.4 \
    pandas==2.2.2 \
    peft==0.8.2 \
    torch==2.1.2 \
    tqdm==4.66.4 \
    transformers==4.39.0 \
    absl-py==2.1.0 \
    einops==0.8.0 \
    einops-exts==0.0.4 \
    imageio==2.35.1 \
    natsort==8.4.0 \
    pillow==10.2.0 \
    scipy==1.12.0 \
    samplings==0.1.7


COPY postInstall /
RUN /postInstall
