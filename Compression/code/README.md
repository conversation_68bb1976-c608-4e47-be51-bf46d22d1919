# LLM For Compress
This is the code of **Understanding is Compression**. 
# Hardware and Software
Our experiments are conducted on a computational node equipped with a single NVIDIA A100 GPU (40GB), six Intel(R) Xeon(R) Gold 6248R CPUs running at 3.00GHz, and 45GB of RAM.
Some third-party libraries used in our experiments are located in the third_party folder.
# Reproducible Run
We provide a script to get started, named run.sh. To run with different data, you only need to modify the parameters within the script. We also include the command to reproduce the baseline results in run.sh
The data and model are located in the third_party folder. Due to the runtime limitations of the CodeOcean platform, our code by default only compresses one file. If you wish to run the entire process, please download the code and execute it locally.