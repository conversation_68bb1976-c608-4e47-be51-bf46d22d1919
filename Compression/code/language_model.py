from __future__ import annotations

import functools
import os.path
from dataclasses import dataclass, field
from typing import Callable, Optional, Iterator, Union
import numpy as np
import e<PERSON>ps
import torch
from matplotlib import pyplot as plt
import time
from collections import Counter
from PIL import Image
from peft import LoraConfig, get_peft_model
from torchvision.transforms import Compose
from transformers import ImageGPTImageProcessor, ImageGPTForCausalImageModeling, ImageGPTConfig, AutoModelForCausalLM, \
    AutoTokenizer, ImageGPTFeatureExtractor, LlamaForCausalLM
import sys
import constants
from utils import arithmetic_coder
from utils.ac_utils import  normalize_pdf_for_arithmetic_coding, bits_to_bytes, bytes_to_bits
import data_loaders

import pdb
from collections import Counter


model, image_processor, tokenizer = None, None, None

device_id = 0
device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() else 'cpu')
# device = 'cuda'




def _retrieve_model_params(model_type, model_path=Union[str, functools.partial], ):
    global model, image_processor, tokenizer
    if not isinstance(model, torch.nn.Module):
        if model_type=="igpt":
            model_path = '/home/<USER>/guest_lizg/codeocean_submit/LMCompress/third_party/igpt'
            model = ImageGPTForCausalImageModeling.from_pretrained(model_path)
            image_processor = ImageGPTImageProcessor.from_pretrained(model_path)
            model.to(device)

    return model, image_processor, tokenizer


def _retrieve_predict_fn(
        model: torch.nn.Module,
        image_processor,
        tokenizer,
        is_lvm: bool = False
) -> Callable[[np.ndarray], np.ndarray]:
    """Returns the prediction function for the trained model."""

    def get_pdf(_input):

        # 多通道图像，将多通道图像的r，g，b分开，而不进行color量化
        if hasattr(image_processor, "do_color_quantize") and not image_processor.do_color_quantize:
            image_processor.do_color_quantize = True
        # 暂时定为通道大于1才量化
        if isinstance(image_processor, Compose):
            input_ids = image_processor(Image.fromarray(_input[0]))
            input_ids = einops.rearrange(input_ids, "c h w ->1 1 1 c h w")

            instruct = "Please describe the image."
            tokenizer.padding_side = "right"
            lang_tensor = tokenizer(instruct, return_tensors="pt")['input_ids']
            # lang_tensor = einops.repeat(lang_tensor, 'n -> b n', b=1)
            with torch.no_grad():
                output = model(vision_x=input_ids,
                               lang_x=lang_tensor,
                               output_hidden_states=True)
            gen_sequence_probs = output['logits'].to(torch.float32).squeeze().softmax(-1).cpu().detach().numpy()

        elif is_lvm:
            with torch.no_grad():
                # TODO 使用deepspeed进行推理
                _input = einops.rearrange(_input, " h w (c b) -> b c h w", b=1)
                _input = _input / 255.0
                _input = _input.astype(np.float32)
                input_ids = tokenizer.get_code(torch.tensor(_input).to(device))
                hidden_states = model.generate(input_ids,
                                      attention_mask=torch.ones_like(input_ids),
                                      pad_token_id=8192,
                                      max_new_tokens=256,
                                      output_scores=True,
                                      return_dict_in_generate=True,
                                      suppress_tokens=list(range(8192, model.vocab_size)), )
                token_scores = torch.stack([item for item in hidden_states.scores], dim=0)
                token_scores = einops.rearrange(token_scores, "n b d -> b n d")
                new_tokens = torch.argmax(torch.softmax(token_scores, dim=-1), dim=-1)
                pixel_logit = tokenizer.decode_code(new_tokens, True)
                pixel_logit = einops.rearrange(pixel_logit, "b (c d) h w -> b h w c d", c=3).squeeze()
                gen_sequence_probs = pixel_logit.squeeze().softmax(-1).cpu().detach().numpy()
                # input_data = _input.to(device).to(torch.bfloat16)
                # b, n = input_data.shape[:2]
                # input_data = einops.rearrange(input_data, 'b n c h w -> (b n) c h w')
                # input_ids = image_processor.module.encode(input_data)[1]
                # h = 16
                # w = 16
                # # input_ids = einops.rearrange(input_ids, '(b n) -> b n', b=b, n=h * w * n).contiguous()
                # input_ids = input_ids.view(1, -1)
                # print(input_ids)
                # output = model(input_ids, return_hidden_state=True)
                # output = einops.rearrange(output, 'b (n h w) c -> (b n) c h w', b=b, n=n, h=h, w=w)
                # logits = image_processor.module.decoder(output)
                # logits = logits.view(-1, 256)
                # test_softmax = torch.nn.functional.softmax(logits, dim=-1)
                # gen_sequence_probs = test_softmax.detach().to(torch.float32).cpu().numpy()
        else:
            with torch.no_grad():
                _input = torch.tensor(_input, dtype=torch.int64).to(device)
                output = model(_input, output_hidden_states=True)
            gen_sequence_probs = output['logits'].squeeze().softmax(-1).cpu().detach().numpy()

        # 计算概率分布
        return gen_sequence_probs

    # samples = output[:, 1:].cpu().detach().numpy()
    # # convert color cluster tokens back to pixels
    # samples_img = [np.reshape(np.rint(127.5 * (clusters[s] + 1.0)), [n_px, n_px, 3]).astype(np.uint8) for s in samples]
    #
    # f, axes = plt.subplots(1, 1, dpi=300)
    # for img, ax in zip(samples_img, axes):
    #     ax.axis('off')
    #     ax.imshow(img)
    return get_pdf


def _retrieve_predict_llama3_fn(model, tokenizer):
    def get_pdf(_input):
        # input_ids = tokenizer(_input, return_tensors='pt', padding=True)['input_ids'].to('cuda')
        with torch.no_grad():
            output = model(_input)
        gen_sequence_probs = output['logits'].squeeze().softmax(-1).cpu().detach().numpy()
        return gen_sequence_probs
    return get_pdf


def probs_normalization(probs, prob_value, index):
    current_last_prob = probs[index, -1]
    remaining_prob = 1 - prob_value
    # 计算其他元素的缩放因子
    scaling_factor = remaining_prob / (1 - current_last_prob)
    # 调整其他元素的概率
    probs[index, :-1] *= scaling_factor
    # 设置新的概率值
    probs[index, -1] = prob_value
    return probs
# 重写的关键函数，用模型实现压缩
def compress(
        # data: bytes,
        data,
        return_num_padded_bits: bool = False,
        use_slow_lossless_compression: bool = False,
        use_diff_encode: bool = False,
        model_path = None
) -> bytes | tuple[bytes, int] | tuple[bytes, np.ndarray]:
    """Compresses the `data` using arithmetic coding and a pretrained model.

  Args:
    data: The data to be compressed.
    return_num_padded_bits: Whether to return the number of zeros added to the
      encoded bitstream in order to make it byte-decodeable (i.e., divisible by
      8). Usually, this is used when the encoded data has to be decoded again.
    use_slow_lossless_compression: Whether to compute the `pdf`s for all tokens
      in the data stream in one go or separately for every proper subsequence.
      When only compressing data (i.e., without decompression) use the first
      approach (i.e., `True`) since it has an O(n) runtime complexity, while the
      latter is O(n^2). However, the goal is to losslessly decompress the
      compressed output, use the second option (i.e., `False`) since this is
      what happens in the decoder (which iteratively reconstructs the sequence).
    use_diff_encode: Whether to use difference encode
  Returns:
    The compressed data.
  """
    t0 = time.perf_counter()
    model_type = 'igpt'
    is_lvm = False
    params, image_processor, tokenizer = _retrieve_model_params(model_type, model_path)

    # predict_fn = _retrieve_predict_llama3_fn(params, tokenizer)
    predict_fn = _retrieve_predict_fn(params, image_processor, tokenizer, is_lvm)
    t1 = time.perf_counter()
    sequence_array = data
    if isinstance(sequence_array, tuple):
        previous_array, sequence_array = data
    else:
        previous_array = sequence_array

    diff = np.asarray([x - y for x, y in zip(sequence_array, previous_array)]).flatten('C')

    if not hasattr(sequence_array, 'shape'):
        sequence_array = tokenizer(data, return_tensors='pt')['input_ids'].to(device)
    else:
        if not len(sequence_array.shape) > 3:
            # 说明是多通道压缩
            if sequence_array.shape[-1] == 3:
                # print(f"_input_shape: {_input.shape[-1]}")
                if model_type != 'lvm':
                    sequence_array = image_processor(sequence_array, return_tensors='pt').to(device)['input_ids']
                    previous_array = image_processor(previous_array, return_tensors='pt').to(device)['input_ids']
            else:
                if model_type == 'igpt':

                    test1 = Counter(sequence_array.reshape(-1))
                    # print(f"\nraw count: {test1}, length: {len(test1)}\n")
                    sequence_array = np.repeat(sequence_array, 3, axis=2)
                    sequence_array = image_processor(sequence_array, return_tensors='pt').to(device)['input_ids'].to(torch.int64)
    # use_slow_lossless_compression = True
    if use_slow_lossless_compression:
        log_probs = list()
        for subsequence_length in range(1024):
            subsequence_probs = predict_fn(
                sequence_array[:, : subsequence_length + 1]
            )
            if len(subsequence_probs.shape) < 2:
                log_probs.append(subsequence_probs)
            else:
                log_probs.append(subsequence_probs[-1])
            print(subsequence_length)
        # pdb.set_trace()

        log_probs = np.vstack(log_probs)
    else:
        # 一次性直接预测对数概率分布
        # log_probs = predict_fn(sequence_array[None])
        #TODO 进行的改动,sequence_array没有经过tokenizer，现在是字符串
        log_probs = predict_fn(sequence_array)
    raw_probs = log_probs.reshape(-1, log_probs.shape[-1])
    t2 = time.perf_counter()
    output = list()
    # 算术编码编码器
    encoder = arithmetic_coder.Encoder(
        base=constants.ARITHMETIC_CODER_BASE,
        precision=constants.ARITHMETIC_CODER_PRECISION,
        output_fn=output.append,
    )

    # sequence_array = sequence_array.reshape(-1)
    sequence_array = sequence_array.detach().view(-1).cpu().numpy().squeeze()
    # previous_array = previous_array.detach().view(-1).cpu().numpy().squeeze()
    # max_indices = np.argmax(raw_probs, axis=1)
    # pdb.set_trace()
    # predict_right = sequence_array - max_indices
    # zeros = np.asarray(np.where(predict_right == 0)).squeeze()
    # print(f"predict right rate: {len(zeros) / len(sequence_array)}")
    if use_diff_encode:
        predict_tokens = [np.argmax(item) for item in raw_probs]
        # 计算前一帧和后移帧的差异
        # diff = np.asarray([x - y for x, y in zip(sequence_array, previous_array)])
        # 找出diff中0的位置
        zero_location = np.asarray(np.where(diff == 0)).squeeze()
        # print(f"token zero rate: {len(zero_location) / len(diff)}")

        previous_token = np.full([raw_probs.shape[0], 1], 0)
        new_probs = np.concatenate([raw_probs, previous_token], axis=1)
        for zero_item in zero_location:
            sequence_array[zero_item] = len(new_probs[zero_item]) - 1
        for index in range(raw_probs.shape[0]):
            new_probs = probs_normalization(new_probs, 0.66, index)
        # best 0.28 for bowing
        # best 0.66  for akiyo
        for pdf, symbol in zip(new_probs, sequence_array):
            encoder.encode(normalize_pdf_for_arithmetic_coding(pdf), symbol)
    else:
        # pdb.set_trace()
        symbols = []
        correct_symbols = []
        correct_pdf = []
        counter = 0
        # raw_probs = raw_probs[:-64]
        # sequence_array = sequence_array[:-64]
        for pdf, symbol in zip(raw_probs, sequence_array):

            symbols.append(symbol)
            if max(pdf) == pdf[symbol]:
                correct_symbols.append(symbol)
            correct_pdf.append(pdf[symbol])
            # if (counter + 1) % 1000 == 0:
                # print(f"correct probability {pdf[symbol]}")
                # print(f"current symbol {symbol}")
                # print(f"mean of correct probability {np.mean(correct_pdf)}")
                # test2 = Counter(sequence_array)
                # print(f"tokenizer count: {test2}, length: {len(test2)}\n")
                # print(test2)
                # print(max(test2))
                # pdb.set_trace()
            encoder.encode(normalize_pdf_for_arithmetic_coding(pdf), symbol)
            counter += 1
        # print(len(right_symbols))
        # print(len(right_symbols) / len(symbols))
        # print(right_symbols)

    # 测试编码
    #
    t4 = time.perf_counter()
    # print(f"本次压缩时间:{t4 - t3}")
    encoder.terminate()
    # 将编码后的bit转换为字符串类型
    compressed_bits = ''.join(map(str, output))
    # 将bit位转换为字节bytes
    compressed_bytes, num_padded_bits = bits_to_bytes(compressed_bits)

    if return_num_padded_bits:
        return compressed_bytes, num_padded_bits
    t5 = time.perf_counter()
    # print(f"time cost:{t5 - t0}")

    # test the decoder
    # decompress(compressed_bytes, 0, len(sequence_array))
    return compressed_bytes, log_probs


count = 0


def decompress(
        data: bytes,
        num_padded_bits: int = 0,
        uncompressed_length: int = constants.CHUNK_SIZE_BYTES,
) -> bytes:
    """Decompresses the `data` using arithmetic coding and a pretrained model.

  See https://en.wikipedia.org/wiki/Arithmetic_coding for details.

  Args:
    data: The data to be decompressed.
    num_padded_bits: The number of zeros added to the encoded bitstream in order
      to make it byte-decodeable (i.e., divisble by 8).
    uncompressed_length: The length of the original data stream (in bytes).

  Returns:
    The decompressed data.
  """
    params, image_processor, tokenizer = _retrieve_model_params("igpt")
    predict_fn = _retrieve_predict_fn(params, image_processor, tokenizer)
    data_iter = iter(bytes_to_bits(data, num_padded_bits=num_padded_bits))

    # The decoder requires a function that reads digits from {0, 1, ..., base - 1}
    # from the compressed input and returns `None` when the input is exhausted.
    def _input_fn(bit_sequence: Iterator[str] = data_iter) -> int | None:
        try:
            global count
            count += 1
            print(count)
            return int(next(bit_sequence))
        except StopIteration:
            return None

    decoder = arithmetic_coder.Decoder(
        base=constants.ARITHMETIC_CODER_BASE,
        precision=constants.ARITHMETIC_CODER_PRECISION,
        input_fn=_input_fn,
    )

    sequence_array = np.asarray((512,))
    probs = np.atleast_2d(predict_fn(sequence_array[:, np.newaxis]))
    result = bytes_to_bits(data)
    print(f"编码结果:{result}，长度为{len(result)}")
    # 递归生成，递归解码
    for idx in range(uncompressed_length):
        token = decoder.decode(
            normalize_pdf_for_arithmetic_coding(prob[idx])
        )
        print(f"当前已解码token数{idx}, 解码的token为{token}")

        # ？倒着插入的
        sequence_array = np.insert(sequence_array, sequence_array.shape[0], token)
        probs = predict_fn(sequence_array[:, np.newaxis])

    # Remove the dummy token and convert to bytes.
    return sequence_array[:-1].tobytes()


if __name__ == '__main__':
    model_path = r'F:\LLM\language_model_is_compression_pytorch\imageGPT'
    data_iterator = data_loaders.get_imagenet_iterator()
    for i in data_iterator:
        plt.subplot(121)
        plt.imshow(i)
        compressed_bytes, prob = compress(i)
        # image_bytes = decompress(compressed_bytes)
        # image_bit = bytes_to_bits(image_bytes)
