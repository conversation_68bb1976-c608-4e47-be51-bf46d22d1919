# Copyright 2023 DeepMind Technologies Limited
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

"""Defines the compressor interface."""
from __future__ import annotations

import functools
import gzip
import lzma
from typing import Mapping, Protocol

import sys


import language_model


class Compressor(Protocol):

    def __call__(self, data: bytes, *args, **kwargs) -> bytes | tuple[bytes, int]:
        """Returns the compressed version of `data`, with optional padded bits."""


COMPRESSOR_TYPES = {
    'classical': ['gzip'],
    'arithmetic_coding': ['language_model'],
}

COMPRESS_FN_DICT: Mapping[str, Compressor] = {
    'gzip': functools.partial(gzip.compress, compresslevel=9),
    'language_model': language_model.compress,
}
