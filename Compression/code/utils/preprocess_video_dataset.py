import glob
import os
import pdb
import subprocess

import argparse


def y4m2png(data_root_path):
    video_files = glob.glob(os.path.join(data_root_path, "*.y4m"))

    for video_file in video_files:
        video_name = os.path.basename(video_file).replace('.y4m', '')
        video_output_dir = os.path.join(data_root_path, video_name)
        if not os.path.exists(video_output_dir):
            os.makedirs(video_output_dir)
        command = [
            'ffmpeg', '-i', video_file,  # 输入文件
            os.path.join(video_output_dir, f'{video_name}_%04d.png')  # 输出PNG文件，%04d表示帧ID
        ]
        subprocess.run(command)

        print(f"{video_name} finished")

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_root_path", type=str)
    args = parser.parse_args()
    args.data_root_path = "/home/<USER>/guest_lizg/data/raw_video/4k_data"
    y4m2png(args.data_root_path)

