# process LibriSpeech data and LJSpeech data
import argparse
from audioop import lin2lin, bias
from glob import glob, iglob
import os
from pydub import AudioSegment
from tqdm import tqdm
from typing import List
import wave


def preprocess(
    mode: str, volume: int, ext: str, split: List[str], path: str, dest: str
) -> None:

    assert path is not None and os.path.exists(path), "Incorrect data path"
    assert dest is not None and os.path.exists(dest), "Incorrect destination"

    volume = volume * pow(2, 10)
    cum_volume = 0

    match mode:
        case "with_split":
            audio_files = []
            LibriSpeech_dir = path
            sections = split
            for section in sections:
                print(f"Select split {section} in {path}")
                sub_secs = glob(LibriSpeech_dir + section + f"/**/*.{ext}", recursive=True)
                audio_files.extend(sub_secs)

            for _, af in tqdm(enumerate(audio_files), total=len(audio_files)):
                if cum_volume > volume:
                    break
                audio_segment = AudioSegment.from_file(af)
                audio_segment.export(
                    "workspace.wav",
                    format="wav",
                    parameters=["-acodec", "pcm_s16le", "-ar", "16000"],
                )

                with wave.open("workspace.wav") as f:
                    # header, contains num of channels, width, sample rate, num of frames
                    # since we are compressing knowing these parameters, these can be ignored
                    metadata = f.getparams()
                    # read bytes
                    audio_frames = f.readframes(metadata.nframes)
                    # convert to width 1
                    audio_frames = bias(lin2lin(audio_frames, 2, 1), 1, 128)

                    naf = os.path.join(
                        dest, os.path.basename(af).replace(ext, "wav")
                    )
                    with wave.open(
                        naf,
                        mode="wb",
                    ) as nf:
                        new_metadata = metadata._replace(sampwidth=1)
                        nf.setparams(new_metadata)
                        nf.writeframes(audio_frames)

                stats = os.stat(naf)
                cum_volume += stats.st_size

        case "common":
            # data file is already wav
            audio_files = glob(path + f"**/*.{ext}", recursive=True)

            for _, af in tqdm(enumerate(audio_files), total=len(audio_files)):
                if cum_volume > volume:
                    break
                audio_segment = AudioSegment.from_file(af)
                audio_segment.export(
                    "workspace.wav",
                    format="wav",
                    parameters=["-acodec", "pcm_s16le", "-ar", "16000"],
                )

                with wave.open("workspace.wav") as f:
                    # header, contains num of channels, width, sample rate, num of frames
                    # since we are compressing knowing these parameters, these can be ignored
                    metadata = f.getparams()
                    # read bytes
                    audio_frames = f.readframes(metadata.nframes)
                    # convert to width 1
                    audio_frames = bias(lin2lin(audio_frames, 2, 1), 1, 128)

                    naf = os.path.join(dest, os.path.basename(af).replace(ext, "wav"))
                    with wave.open(naf, mode="wb") as nf:
                        new_metadata = metadata._replace(sampwidth=1)
                        nf.setparams(new_metadata)
                        nf.writeframes(audio_frames)

                stats = os.stat(naf)
                cum_volume += stats.st_size
            
            print(f"Processed {cum_volume} bytes.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", type=str, default="common")
    parser.add_argument("--volume", type=int, default=1024)
    parser.add_argument("--ext", type=str, default="wav")
    parser.add_argument("--split", type=str, default="dev-clean")
    parser.add_argument("--path", type=str, default=None)
    parser.add_argument("--dest", type=str, default=None)

    args = parser.parse_args()

    args.split = args.split.split(",")

    preprocess(args.mode, args.volume, args.ext, args.split, args.path, args.dest)
