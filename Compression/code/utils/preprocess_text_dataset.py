import argparse
import json
import os
import pandas as pd
from tqdm import tqdm


def preprocess(dataset_name: str, max_file_size: int, path: str, dest: str) -> None:

    assert path is not None and os.path.exists(path), "Incorrect data path"
    assert dest is not None and os.path.exists(dest), "Incorrect destination"

    match dataset_name:
        case "MEDAL":
            df = pd.read_csv(path + "medal.csv")
            ndf = []
            file_size = 0
            counter = 0
            for _, row in tqdm(df.iterrows(), total=len(df)):
                if file_size < max_file_size * 1024:
                    ndf.append([row["TEXT"]])
                    file_size += len(row["TEXT"])
                else:
                    ndf = pd.DataFrame(ndf, columns=["TEXT"])
                    ndf.to_csv(dest + f"medal_{counter}.csv")
                    # new
                    counter += 1
                    ndf = []
                    ndf.append([row["TEXT"]])
                    file_size = len(row["TEXT"])
        case "EURLEX":
            ndf = []
            file_size = 0
            counter = 0
            with open(path + "train.eurlex.jsonl") as f:
                lines = f.readlines()
                for line in tqdm(lines, total=len(lines)):
                    try:
                        line = json.loads(line, strict=False)
                    except:
                        continue
                    if file_size < max_file_size * 1024:
                        ndf.append([line["text"]])
                        file_size += len(line["text"])
                    else:
                        ndf = pd.DataFrame(ndf, columns=["TEXT"])
                        ndf.to_csv(dest + f"eurlex_{counter}.csv")
                        # new
                        counter += 1
                        ndf = []
                        ndf.append([line["text"]])
                        file_size = len(line["text"])


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset", type=str, default="MEDAL")
    parser.add_argument("--mfs", type=int, default=100) # in KB
    parser.add_argument("--path", type=str, default=None)
    parser.add_argument("--dest", type=str, default=None)

    args = parser.parse_args()

    preprocess(args.dataset, args.mfs, args.path, args.dest)
