# Copyright 2023 DeepMind Technologies Limited
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

"""Implements data loaders."""
from glob import glob
import pdb

from einops import einops
from natsort import natsorted
from collections.abc import Iterator
import itertools
import os.path


import pandas as pd
import torch
import numpy as np
import imageio
from torch.utils.data import DataLoader
from transformers import ImageGPTImageProcessor, ImageGPTForCausalImageModeling
from PIL import Image
import constants
import sys

def _get_imagenet_dataset(data_path):
    filet_count = 0
    img_files = [os.path.join(data_path, item) for item in os.listdir(data_path)]
    img_files = natsorted(img_files)
    for file in img_files:
        print(f"file: {file}, count: {filet_count}")
        test = imageio.imread_v2(file)

        if test.shape[-1] != 3:
            continue

        yield test, filet_count
        filet_count += 1

def _extract_image_patches(image: np.ndarray) -> Iterator[bytes]:
    h, w = constants.CHUNK_SHAPE_2D
    height, width = image.shape[0], image.shape[1]
    for row, col in itertools.product(range(height // h), range(width // w)):
        yield image[row * h: (row + 1) * h, col * w: (col + 1) * w]


def _extract_image_sequence(image: np.ndarray) -> Iterator[bytes]:
    h, w = constants.CHUNK_SHAPE_2D
    height, width = image.shape[0], image.shape[1]
    total_pixels = height * width
    sequence_length = h * w
    total_chunks = total_pixels // sequence_length
    image_sequence = image.reshape(-1, image.shape[-1])
    for i in range(total_chunks):
        temp_sequence = image_sequence[i * sequence_length: (i + 1) * sequence_length]
        yield temp_sequence.reshape(h, w, image.shape[-1])

def get_imagenet_iterator(
        num_chunks: int = constants.NUM_CHUNKS,
        is_channel_wised: bool = False,
        is_seq: bool = False,
        data_path: str = None,
) -> Iterator[bytes]:

    imagenet_dataset = _get_imagenet_dataset(data_path)
    idx = 0
    image_extractor = _extract_image_sequence if is_seq else _extract_image_patches
    for data, img_id in imagenet_dataset:
        if is_channel_wised:
            for i in range(data.shape[-1]):
                temp_data = data[:, :, i:i+1]
                for patch in image_extractor(temp_data):
                    yield patch, img_id
                    idx += 1
        elif is_seq:
            for patch in image_extractor(data):
                if idx == num_chunks:
                    return
                yield patch, img_id
                idx += 1


def get_raw_video_double_frame_iterator(num_chunks: int = constants.NUM_CHUNKS,
                            is_channel_wised: bool = True,
                            is_seq: bool = False,
                            is_two_frame: bool = False,
                            data_path: str = None):
    from glob import glob
    data = glob(data_path + '/*.png')
    data = natsorted(data)
    data_length = len(data)
    idx = 1
    frame_id = 0
    image_extractor = _extract_image_sequence
    print(f"start compress {data_path}")
    print(f"total frame {data_length}")
    for i in range(data_length):
        if frame_id >= len(data):
            return
        first_frame = process_img(data[frame_id], True)
        print(f"first_frame: {first_frame.shape}")
        h, w, c = first_frame.shape
        print(f"first_frame: {h * w * c / 1024}")
        if is_channel_wised:
            for i in range(first_frame.shape[-1]):
                if is_two_frame:
                    second_frame = np.asarray(Image.open(data[frame_id + 1]).convert('RGB'))
                    temp_data1 = first_frame[:, :, i:i + 1]
                    temp_data2 = second_frame[:, :, i:i + 1]
                    for seq1, seq2 in zip(image_extractor(temp_data1), image_extractor(temp_data2)):
                        yield seq1, seq2
                else:
                    temp_data1 = first_frame[:, :, i:i + 1]
                    for seq1 in image_extractor(temp_data1):
                        yield seq1, frame_id
        else:
            for i in range(first_frame.shape[-1]):
                if is_two_frame:
                    second_frame = np.asarray(Image.open(data[frame_id + 1]).convert('RGB'))
                    yield first_frame, second_frame
                else:
                    yield first_frame, frame_id
        print(f"{frame_id + 1} frame finish compress")
        frame_id += 1
        idx += 1
    print(f"{data_path} finish compress")

def get_diff_frame_iterator(num_chunks: int = constants.NUM_CHUNKS,
                            is_channel_wised: bool = True,
                            is_seq: bool = False,
                            is_two_frame: bool = False,
                            data_path: str = None):
    data_dirs = [os.path.join(item, 'difunsigned') for item in glob.glob(os.path.join(data_path) + '/*') if
                 os.path.isdir(item)]
    image_path = [img_path for item in data_dirs for img_path in glob.glob(item + '/*.png')]
    image_path = natsorted(image_path)
    image_extractor = _extract_image_sequence
    for diff_id, img in enumerate(image_path):
        diff_img = process_img(img, True)
        if is_channel_wised:
            for i in range(diff_img.shape[-1]):
                temp_data1 = diff_img[:, :, i:i + 1]
                for seq1 in image_extractor(temp_data1):
                    yield seq1, diff_id

def RGB2YUV(img):
    img = img.astype(np.float32)

    r, g, b = np.split(img, 3, axis=2)

    u = b - np.round((87 * r + 169 * g) / 256.0)
    v = r - g
    y = g + np.round((86 * v + 29 * u) / 256.0)

    yuv_img = np.concatenate([y, u, v], axis=2)

    return yuv_img

def process_img(path, is_label=False):
    pil_im = Image.open(path).convert('RGB')
    # input_img = pil_im.resize((256, 256))
    input_img = pil_im
    if not is_label:
        input_img = np.array(input_img) / 255.0
        input_img = input_img.astype(np.float32)
    else:
        input_img = np.array(input_img)
    return input_img

def YUV2RGB(img):
    img = img.astype(np.float32)

    y, u, v = np.split(img, 3, axis=2)

    g = y - np.round((86 * v + 29 * u) / 256.0)
    r = v + g
    b = u + np.round((87 * r + 169 * g) / 256.0)

    rgb_img = np.concatenate([r, g, b], axis=2)

    return (rgb_img).astype(np.uint8)

GET_DATA_GENERATOR_FN_DICT = {
    'imagenet': get_imagenet_iterator,
    'raw_video_frames': get_raw_video_double_frame_iterator,

}

