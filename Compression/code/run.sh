#!/bin/bash
export PYTHONUNBUFFERED=1
# These are examples of how to run LMCompress
python compress_with_bgpt_or_llama.py --data_path /root/capsule/data/data/audios/sample/ --data_type wav --model_path  /root/capsule/data/model/bgpt/weights-audio.pth --context_window 8160 --batch_size 1
# python compress_with_bgpt_or_llama.py --data_path  /root/capsule/data/data/images/clic_bmp/ --data_type bmp --model_path /root/capsule/data/model/bgpt/weights-image.pth --context_window 8160 --batch_size 1
# python compress_with_bgpt_or_llama.py --data_path /root/capsule/data/text/MEDAL_sample/ --data_type csv --model_path /root/capsule/data/model/finetuned_llama/Llama-3-8B --lora_path /root/capsule/data/model/finetuned_llama/Llama-3-8B_lora --context_window 8192 --batch_size 1
#python compress_with_bgpt_or_llama.py --data_path /root/capsule/data/images/ILSVRC_bmp/ --data_type bmp --model_path  /root/capsule/data/bgptweights-image.pth --context_window 8160 --batch_size 1
# python compress_with_igpt.py    --dataset imagenet --data_path /root/capsule/data/images/ILSVRC/

# These are examples of how to run baselines
#python compress_with_baseline.py --mode text  --input_dir ./data/vidoes/4k/ --formats zstd
#python compress_with_baseline.py --mode text  --input_dir ./data/vidoes/dynamic/ --formats zstd
#python compress_with_baseline.py --mode text  --input_dir ./data/vidoes/static/ --formats zstd
#python compress_with_baseline.py --mode text  --input_dir ./data/text/MEDAL_sample --formats zstd
#python compress_with_baseline.py --mode audio  --input_dir ./data/audios/LJSpeech/ --formats flac



