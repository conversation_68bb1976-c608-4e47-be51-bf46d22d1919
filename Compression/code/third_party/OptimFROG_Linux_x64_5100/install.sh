#!/bin/sh

# initial version of the script contributed by <PERSON>

set -e

VERSION="5.100"
LIBVER="0"

PREFIX="/usr"
DESTDIR=""
OPTIONS_PROVIDED=""

BINARIES="off ofr ofr_sfx ofs"

usage() {
  cat <<EOF
This script will install OptimFROG system-wide. By default, it will
install into ${PREFIX}. If no arguments are provided, you will be
prompted for configuration options. Otherwise, if arguments are
provided, the script will run in non-interactive mode and perform all
operations without further input.

The following options are available:

-p PREFIX: Set the prefix where OptimFROG will be installed. This
defaults to ${PREFIX}. Binaries will be installed into PREFIX/bin,
libraries into PREFIX/lib, and headers into PREFIX/include.

-d DESTDIR: If provided, this will be used as a staging area, and
OptimFROG will be installed into it (under DESTDIR/PREFIX) instead of
directly into PREFIX. This is intended to aid in software packaging.
EOF
}

while getopts "d:p:h" flag
do
  OPTIONS_PROVIDED="1"

  case "${flag}" in
    "d")
      DESTDIR="${OPTARG}"
      ;;
    "p")
      PREFIX="${OPTARG}"
      ;;
    *)
      printf '\n'
      usage
      exit 1
      ;;
  esac
done

if [ -z "${OPTIONS_PROVIDED}" ]
then
  cat <<EOF
Welcome to the OptimFROG installer. What prefix should OptimFROG install
to?
EOF
  printf '%s' "[${PREFIX}] "
  read var
  [ -n "${var}" ] && PREFIX="${var}"

  cat <<EOF
If you would like to install to a staging area, please enter it now.
Leave blank to not use a staging area.
EOF
  printf '%s' "[] "
  read var
  [ -n "${var}" ] && DESTDIR="${var}"
fi

T="${DESTDIR}${PREFIX}"

printf '\n'
mkdir -p "${T}/bin" "${T}/lib" "${T}/include/OptimFROG" "${T}/lib/pkgconfig"

install -v -p -m 755 ${BINARIES} "${T}/bin"
install -v -p -m 755 "SDK/Library/libOptimFROG.so.${LIBVER}" "${T}/lib"
ln -v -s -f "libOptimFROG.so.${LIBVER}" "${T}/lib/libOptimFROG.so"
install -v -p -m 644 SDK/OptimFROG/*.h "${T}/include/OptimFROG"
printf '%s\n' "creating ${T}/lib/pkgconfig/optimfrog.pc using optimfrog.pc.in"
cat optimfrog.pc.in | sed -e "s!@@PREFIX@@!${PREFIX}!g" | sed -e "s!@@VERSION@@!${VERSION}!g" > "${T}/lib/pkgconfig/optimfrog.pc"
printf '\n'

printf '%s\n' "Installed to ${T}"
