<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<HTML>
<HEAD>
  <META http-equiv="Content-Type" content="text/html; charset=utf-8">
  <META name="Author" content="Florin Ghido">
  <META name="Keywords" content="OptimFROG, free, best, lossless, audio, compression, coding, compressor, codec, encoder, decoder, archive, DualStream, near-lossless, hybrid, scalable, music, OFR, plug-in">
  <META name="Description" content="OptimFROG, the best lossless audio codec avaliable.">
  <TITLE>OptimFROG - SDK Interface Documentation</TITLE>
</HEAD>

<BODY>

<div class="Content" align="justify">


<br>
<h3>OptimFROG - SDK Interface Documentation</h3>
<br>


<br>
The current version of the OptimFROG SDK is 1.600, which corresponds to OptimFROG version 5.100 or newer. Starting with OptimFROG version 5.000, the entire OptimFROG SDK is included in the corresponding OptimFROG release package for each supported platform.<br>
<br>
This OptimFROG SDK interface is a <font color="red">release version</font> and binary compatibility will be assured with future releases. This means that applications compiled with this or future releases will work seamlessly using the binary Library of any future release. No changes will be made to this interface, aside from problem fixes and adding new features. The first stable version of OptimFROG SDK was 1.100, released on 2004-04-21.<br>
This release version <font color="red">is not binary compatible with the 1.000e</font> (the first, experimental version), so you are strongly encouraged to recompile your application (without any source file modifications on your side required in order to recompile).<br>
<br>
The OptimFROG SDK is quite simple to use and provides interfaces to the binary DLL/SO/DYLIB Libraries for working with OptimFROG Lossless (OFR) and DualStream (OFS) files. The SDK includes a C-style interface (which can be used from both C and C++) and a thin C++ class wrapper interface. There is also the very simple possiblity of processing an entire compressed file with a single function call. File information retrieval, ID3v1.0, ID3v1.1, and APEv2.0 tags reading, 64 bit file support, fast seek, custom reading stream functions, bitstream error resilience are only a few relevant features of this interface and OptimFROG.<br>
<br>
<br>
<font size="+1">Quick contents:</font><br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#structures"><font size="+1">OptimFROG SDK interface data structures</font></a><br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#functions"><font size="+1">OptimFROG SDK interface functions</font></a><br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#codes"><font size="+1">OptimFROG SDK interface return result codes</font></a><br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="#list"><font size="+1">OptimFROG SDK interface file listing</font></a><br>
<br>
<br>
<br>
<br>


<a name="structures"><font size="+2">OptimFROG SDK interface data structures</font></a>

<font color="blue"><pre>
typedef struct
{
  condition_t (*close)(void* instance);
  sInt32_t (*read)(void* instance, void* destBuffer, uInt32_t count);
  condition_t (*eof)(void* instance);
  condition_t (*seekable)(void* instance);
  sInt64_t (*length)(void* instance);
  sInt64_t (*getPos)(void* instance);
  condition_t (*seek)(void* instance, sInt64_t pos);
} ReadInterface;
</pre></font>

This structure is used to create custom stream reading functions for use with the OpimFROG_openExt function. The read, length, and getPos functions return -1 on errors. The eof function is not used for the moment. For a detailed example of how to use this, please see the foobar2000 decoder plug-in source.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
typedef struct
{
  uInt32_t keyCount;
  char* keys[64];
  char* values[64];
} OptimFROG_Tags;
</pre></font>

This structure is used to retrieve information about the tags (ID3v1.0, ID3v1.1, and APEv2.0 are supported) which are present in an OFR/OFS file. The keyCount contains the number of the tag keys. The key-value pairs are keys[0] and values[0], ..., up to keys[keyCount - 1] and values[keyCount - 1].<br>
If no tags are found in the file (or in case of an error), the keyCount is set to 0.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
typedef struct
{
  uInt32_t channels;
  uInt32_t samplerate;
  uInt32_t bitspersample;
  uInt32_t bitrate;

  uInt32_t version;
  const char* method;
  const char* speedup;

  sInt64_t noPoints;
  sInt64_t originalSize;
  sInt64_t compressedSize;
  sInt64_t length_ms;

  const char* sampleType;
  const char* channelConfig;
} OptimFROG_Info;
</pre></font>

This structure is used to retrieve information about the format, compression, and size of an OFR/OFS file. All throughout this interface only "points" are used for counting the number of samples. The "point" has the advantage of simpler operation and cannot produce problems because the number of samples is not a multiple of the number of channels. A "point" is a group of samples with number equal with the number of channels which represents the same time instant. For example, a stereo "point" contains two samples.<br>
The bitspersample is the number of bits used for the *container* data type of the sample values, and is always a multiple of 8 (integer number of bytes). For example, a 20 bit file will have bitspersample 24 bits.<br>
The bitrate is expressed in kbits/sec where 1 kbit = 1000 bits. It is important to note that 1 kbit is *not* equal with 1024 bits.<br>
The compressed size is set to 0 if the underlying stream cannot report the compressed file size, and the bitrate is set to the bitrate of the uncompressed data.<br>
The sampleType is a textual representation of the data format, useful for example when interested in finding the actual data type (differentiating between SINT16 and UINT16, for example is not possible using only the bitspersample field).
<br>
<br>
<br>
<br>

<font color="blue"><pre>
typedef void (*OptimFROG_callBack)(void* callBackParam, Float64_t percentage);
</pre></font>
This is used for reporting progress information during some functions. The function is called by the internal engine to report progress state. The callBackParam parameter is the value passed as callback when the function which reports progress was initially called. This is useful, for example, to display the progress into a GUI interface, where a handle of the current window or custom class address is needed for the display process. The percentage value is between 0 and 100 inclusive.
<br>
<br>
<br>
<br>


<a name="functions"><font size="+2">OptimFROG SDK interface functions</font></a>


<font color="blue"><pre>
uInt32_t OptimFROG_getVersion();
</pre></font>
Returns the OptimFROG binary Library codec version as an integer, i.e., 4509 represents version 4.509.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
void* OptimFROG_createInstance();
</pre></font>
Creates an instance of a decoder object for later use in the other functions. If the object cannot be created (mostly because of not enough memory, or because a binary Library built with SSE2 enabled was used on a x86 processor not supporting the SSE2 instruction set), C_NULL is returned.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
void OptimFROG_destroyInstance(void* decoderInstance);
</pre></font>
Destroys an instance of a decoder object, freeing all the memory and resources associated with it.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_openExt(void* decoderInstance, ReadInterface* rInt,
                              void* readerInstance, condition_t readTags = C_FALSE);
</pre></font>
Opens extended an OFR/OFS stream, using the rInt ReadInterface functions for reading from the underlying stream, using the stream instance readerInstance. The readTags specifies if the tags should be read from the stream, if possible.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_open(void* decoderInstance, char* fileName,
                           condition_t readTags = C_FALSE);
</pre></font>
Opens an OFR/OFS file named fileName, using the default internal underlying stream. The readTags specifies if the tags should be read from the file, if possible.
<br>
<br>
<br>
<br>


<font color="blue"><pre>
condition_t OptimFROG_close(void* decoderInstance);
</pre></font>
Closes an instance of a decoder object, releasing the stream resources associated with it.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
sInt32_t OptimFROG_readHead(void* decoderInstance, void* headData, uInt32_t maxSize);
</pre></font>
Reads the header data of the original file, which contains everything before the raw audio data (i.e., the original file header, like the WAV header). For headerless raw audio data files, the header data has size zero. The maxSize represents the maximum size which can be put in the headData buffer. If the result of the function is bigger than maxSize, then the buffer is *not* modified and you must call the function again with a sufficient maxSize value (and a bigger buffer).
<br>
<br>
<br>
<br>

<font color="blue"><pre>
sInt32_t OptimFROG_readTail(void* decoderInstance, void* tailData, uInt32_t maxSize);
</pre></font>
Reads the footer data of the original file, which contains everything after the raw audio data (i.e., the original file footer, like cuesheets for WAV files). The maxSize represents the maximum size which can be put in the tailData buffer. If the result of the function is bigger than maxSize, then the buffer is *not* modified and you must call again with a sufficient maxSize value (and a bigger buffer).
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_seekable(void* decoderInstance);
</pre></font>
Checks whether the instance is seekable, checking the ability to seek of the underlying stream.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_seekPoint(void* decoderInstance, sInt64_t samplePoint);
</pre></font>
Seeks to the samplePoint "point" position in the data. If the underlying stream is not seekable, the decoder enters a state where you cannot read anymore from it. You should not attempt a seek if the OptimFROG_seekable returns it is not seekable.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_seekTime(void* decoderInstance, sInt64_t milliseconds);
</pre></font>
Seeks to the milliseconds position in the data. If the underlying stream is not seekable, the decoder enters a state where you cannot read anymore from it. You should not attempt a seek if the OptimFROG_seekable returns it is not seekable.
<br>
<br>
<br>
<br>
<br>

<font color="blue"><pre>
sInt64_t OptimFROG_getPos(void* decoderInstance);
</pre></font>
Returns the current "point" position from the instance. This function does not fail, even when the underlying stream is not seekable.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_recoverableErrors(void* decoderInstance);
</pre></font>
Tells whether recoverable errors appeared in the file (i.e., a broken data block, which is replaced with silence).
<br>
<br>
<br>
<br>

<font color="blue"><pre>
sInt32_t OptimFROG_read(void* decoderInstance, void* data, uInt32_t noPoints,
                        condition_t max16bit = C_FALSE);
</pre></font>
Reads up to noPoints points from the instance, less if there are less remaining points in the instance. The data is packed exactly as in the original file. Currently, OptimFROG supports only sample formats in little-endian byte order. If you set the max16bit flag, the data which is 24 bit or 32 bit is converted to 16 bit values (and packed as it would be 16 bit). The data buffer must have enough size available to hold the data put, i.e., should have the size noPoints * (info.bits / 8) * info.channels in bytes. In the case of unrecoverable errors, this function returns -1.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_getInfo(void* decoderInstance, OptimFROG_Info* info);
</pre></font>
Fills the info structure with information from the instance. The information is computed internally only once, after opening the file, so further calls will return exactly the same data. The bitrate field value is the average bitrate over the entire file.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
condition_t OptimFROG_getTags(void* decoderInstance, OptimFROG_Tags* tags);
</pre></font>
Fills the tags structure with information from the instance. If the file was open with readTags set to C_FALSE, the tags structure returned will be empty.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
void OptimFROG_freeTags(OptimFROG_Tags* tags);
</pre></font>
Releases the memory allocated for the key and value pairs in the tags structure. This function was added in SDK version pr1.200, released on 2005-07-17, in order to avoid a potential memory ownership issue, due to deallocation by the application of the memory allocated by the binary Library. The function should be called when the tags structure is not needed anymore, instead of manually deallocating the memory for each of the key and value pairs.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
sInt32_t OptimFROG_decodeFile(char* sourceFile, char* destinationFile,
                              OptimFROG_callBack callBack = C_NULL,
                              void* callBackParam = C_NULL);
</pre></font>
Decodes an entire file with the name sourceFile to the file named destinationFile. If you want progress information you must supply a callback function. The function returns a result code with the significance described in the next section.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
sInt32_t OptimFROG_infoFile(char* sourceFile, OptimFROG_Info* info,
                            OptimFROG_Tags* tags = C_NULL);
</pre></font>
Fills the info structure and the tags structure with information from the specified file with the name sourceFile. If tags is C_NULL (i.e., you are not interested in the tags), the tags information is not read from the file. The function returns a result code with the significance described in the next section.
<br>
<br>
<br>
<br>


<a name="codes"><font size="+2">OptimFROG SDK interface return result codes</font></a>
<br>
<br>
The return result codes are used by the OptimFROG_decodeFile and OptimFROG_infoFile functions.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
#define OptimFROG_NoError 0
</pre></font>
No errors detected.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
#define OptimFROG_MemoryError 1
</pre></font>
The process failed because of insufficient memory.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
#define OptimFROG_OpenError 2
</pre></font>
The process failed because the source file cannot be open.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
#define OptimFROG_WriteError 3
</pre></font>
The process failed because the destination file cannot be open or a write failed on the destination file.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
#define OptimFROG_FatalError 4
</pre></font>
The process failed because of a fatal error, like a truncated source file.
<br>
<br>
<br>
<br>

<font color="blue"><pre>
#define OptimFROG_RecoverableError 5
</pre></font>
One or several recoverable errors appeared (i.e., broken data blocks, which are replaced with silence).
<br>
<br>
<br>
<br>


<a name="list"><font size="+2">OptimFROG SDK interface file listing</font></a>
<br>
<br>
The OptimFROG SDK interface folder contains the following files:
<ul>
<li>Library/OptimFROG.dll - the DLL binary Library (for Win/x86, Win/x86+SSE2, and Win/x64)</li>
<li>Library/OptimFROG.lib - the import library corresponding to the above DLL binary Library</li>
<li>Library/libOptimFROG.so - the SO binary Library (for Linux/x86, Linux/x86+SSE2, and Linux/x64)</li>
<li>Library/libOptimFROG.dylib - the DYLIB binary Library (for OSX/x86+SSE2 and OSX/x64)</li>
<br>
<li>Examples/Makefile - used to build the examples, works for each supported release platform</li>
<li>Examples/MakeMSVC.cmd - used to build the examples, for Win/x86, Win/x86+SSE2, and Win/x64</li>
<li>Examples/OFR_Test_Simple.c - an example of using the simple C-style interface</li>
<li>Examples/OFR_Test_Simple.cpp - an example of using the simple C++ class wrapper interface</li>
<li>Examples/OFR_Test.c - an example of using the C-style interface</li>
<li>Examples/OFR_Test.cpp - an example of using the C++ class wrapper interface</li>
<li>Examples/OFR_SFX.c - an example illustrating the official self-extracting stub code</li>
<br>
<li>OptimFROG/OptimFROG.h - the C-style interface header file, usable from both C and C++</li>
<li>OptimFROG/OptimFROGDecoder.h - a thin C++ class wrapper around the C-style interface</li>
<li>OptimFROG/PortableTypes.h - internal file, defines portable data types and identifiers</li>
<li>OptimFROG/SystemID.h - internal file, detects the currently used build configuration</li>
<br>
<li>Documentation/SDK.html - SDK interface specification and documentation (this file)</li>
<li>Documentation/format.txt - compressed file format specification and documentation</li>
</ul>


<br>
<p align="center">
OptimFROG Lossless + DualStream + IEEE Float Audio Codec v5.xxx
<br>
Copyright (C) 1996-2016 Florin Ghido, all rights reserved.
<br>
Visit http://LosslessAudio.org/ for updates and more information.
<br>
@OptimFROG is also on Twitter. E-mail: florin &#183; ghido &#177; gmail &#183; com
</p>


</div>

</BODY>
</HTML>
