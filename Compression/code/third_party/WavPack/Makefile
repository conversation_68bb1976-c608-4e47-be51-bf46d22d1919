# Makefile.in generated by automake 1.16.2 from Makefile.am.
# Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994-2020 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.







am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/wavpack
pkgincludedir = $(includedir)/wavpack
pkglibdir = $(libdir)/wavpack
pkglibexecdir = $(libexecdir)/wavpack
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-pc-linux-gnu
host_triplet = x86_64-pc-linux-gnu
bin_PROGRAMS = cli/wavpack$(EXEEXT) \
	cli/wvunpack$(EXEEXT) cli/wvgain$(EXEEXT) \
	cli/wvtag$(EXEEXT)
#am__append_1 = cli/win32_unicode_support.c
#am__append_2 = cli/win32_unicode_support.c
#am__append_3 = cli/win32_unicode_support.c
#am__append_4 = cli/win32_unicode_support.c
check_PROGRAMS = cli/wvtest$(EXEEXT)
#am__append_5 = src/unpack3.c src/unpack3_open.c src/unpack3_seek.c
am__append_6 = src/pack_dsd.c src/unpack_dsd.c
#am__append_7 = src/pack_x86.S src/unpack_x86.S
am__append_8 = src/pack_x64.S src/unpack_x64.S
#am__append_9 = src/unpack_armv7.S
##am__append_10 = wavpackdll/dummy.c wavpackdll/wavpackdll.rc wavpackdll/resource.h
src_libwavpack_la_DEPENDENCIES =  \
	$(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
src_libwavpack_la_DEPENDENCIES =  \
	$(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(dist_doc_DATA) $(dist_html_DATA) \
	$(dist_pdf_DATA) $(noinst_HEADERS) $(pkginclude_HEADERS) \
	$(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_CLEAN_FILES = wavpack.pc
CONFIG_CLEAN_VPATH_FILES = cli/all-tests cli/fast-tests
am__installdirs = "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" \
	"$(DESTDIR)$(man1dir)" "$(DESTDIR)$(docdir)" \
	"$(DESTDIR)$(htmldir)" "$(DESTDIR)$(pdfdir)" \
	"$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(pkgincludedir)"
PROGRAMS = $(bin_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
LTLIBRARIES = $(lib_LTLIBRARIES)
am__DEPENDENCIES_1 =
am__src_libwavpack_la_SOURCES_DIST = src/common_utils.c \
	src/decorr_utils.c src/entropy_utils.c src/extra1.c \
	src/extra2.c src/open_utils.c src/open_filename.c \
	src/open_legacy.c src/open_raw.c src/pack.c src/pack_dns.c \
	src/pack_floats.c src/pack_utils.c src/read_words.c src/tags.c \
	src/tag_utils.c src/unpack.c src/unpack_floats.c \
	src/unpack_seek.c src/unpack_utils.c src/write_words.c \
	src/decorr_tables.h src/unpack3.h src/wavpack_local.h \
	src/wavpack_version.h src/unpack3.c src/unpack3_open.c \
	src/unpack3_seek.c src/pack_dsd.c src/unpack_dsd.c \
	src/pack_x86.S src/unpack_x86.S src/pack_x64.S \
	src/unpack_x64.S src/unpack_armv7.S wavpackdll/dummy.c \
	wavpackdll/wavpackdll.rc wavpackdll/resource.h
am__dirstamp = $(am__leading_dot)dirstamp
#am__objects_1 = src/libwavpack_la-unpack3.lo \
#	src/libwavpack_la-unpack3_open.lo \
#	src/libwavpack_la-unpack3_seek.lo
am__objects_2 = src/libwavpack_la-pack_dsd.lo \
	src/libwavpack_la-unpack_dsd.lo
#am__objects_3 = src/pack_x86.lo src/unpack_x86.lo
am__objects_4 = src/pack_x64.lo src/unpack_x64.lo
#am__objects_5 = src/unpack_armv7.lo
##am__objects_6 = wavpackdll/src_libwavpack_la-dummy.lo
am_src_libwavpack_la_OBJECTS = src/libwavpack_la-common_utils.lo \
	src/libwavpack_la-decorr_utils.lo \
	src/libwavpack_la-entropy_utils.lo src/libwavpack_la-extra1.lo \
	src/libwavpack_la-extra2.lo src/libwavpack_la-open_utils.lo \
	src/libwavpack_la-open_filename.lo \
	src/libwavpack_la-open_legacy.lo src/libwavpack_la-open_raw.lo \
	src/libwavpack_la-pack.lo src/libwavpack_la-pack_dns.lo \
	src/libwavpack_la-pack_floats.lo \
	src/libwavpack_la-pack_utils.lo \
	src/libwavpack_la-read_words.lo src/libwavpack_la-tags.lo \
	src/libwavpack_la-tag_utils.lo src/libwavpack_la-unpack.lo \
	src/libwavpack_la-unpack_floats.lo \
	src/libwavpack_la-unpack_seek.lo \
	src/libwavpack_la-unpack_utils.lo \
	src/libwavpack_la-write_words.lo $(am__objects_1) \
	$(am__objects_2) $(am__objects_3) $(am__objects_4) \
	$(am__objects_5) $(am__objects_6)
src_libwavpack_la_OBJECTS = $(am_src_libwavpack_la_OBJECTS)
AM_V_lt = $(am__v_lt_$(V))
am__v_lt_ = $(am__v_lt_$(AM_DEFAULT_VERBOSITY))
am__v_lt_0 = --silent
am__v_lt_1 = 
src_libwavpack_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC \
	$(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=link $(CCLD) \
	$(src_libwavpack_la_CFLAGS) $(CFLAGS) \
	$(src_libwavpack_la_LDFLAGS) $(LDFLAGS) -o $@
am__cli_wavpack_SOURCES_DIST = cli/wavpack.c cli/riff.c cli/wave64.c \
	cli/caff.c cli/dsdiff.c cli/dsf.c cli/aiff.c cli/utils.c \
	cli/md5.c cli/import_id3.c cli/win32_unicode_support.c
#am__objects_7 = cli/wavpack-win32_unicode_support.$(OBJEXT)
am_cli_wavpack_OBJECTS = cli/wavpack-wavpack.$(OBJEXT) \
	cli/wavpack-riff.$(OBJEXT) cli/wavpack-wave64.$(OBJEXT) \
	cli/wavpack-caff.$(OBJEXT) cli/wavpack-dsdiff.$(OBJEXT) \
	cli/wavpack-dsf.$(OBJEXT) cli/wavpack-aiff.$(OBJEXT) \
	cli/wavpack-utils.$(OBJEXT) cli/wavpack-md5.$(OBJEXT) \
	cli/wavpack-import_id3.$(OBJEXT) $(am__objects_7)
cli_wavpack_OBJECTS = $(am_cli_wavpack_OBJECTS)
cli_wavpack_DEPENDENCIES = src/libwavpack.la $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
cli_wavpack_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(cli_wavpack_CFLAGS) \
	$(CFLAGS) $(cli_wavpack_LDFLAGS) $(LDFLAGS) -o $@
am__cli_wvgain_SOURCES_DIST = cli/wvgain.c cli/utils.c \
	cli/win32_unicode_support.c
#am__objects_8 =  \
#	cli/wvgain-win32_unicode_support.$(OBJEXT)
am_cli_wvgain_OBJECTS = cli/wvgain-wvgain.$(OBJEXT) \
	cli/wvgain-utils.$(OBJEXT) $(am__objects_8)
cli_wvgain_OBJECTS = $(am_cli_wvgain_OBJECTS)
cli_wvgain_DEPENDENCIES = src/libwavpack.la $(am__DEPENDENCIES_1)
cli_wvgain_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(cli_wvgain_CFLAGS) \
	$(CFLAGS) $(cli_wvgain_LDFLAGS) $(LDFLAGS) -o $@
am__cli_wvtag_SOURCES_DIST = cli/wvtag.c cli/utils.c cli/import_id3.c \
	cli/win32_unicode_support.c
#am__objects_9 =  \
#	cli/wvtag-win32_unicode_support.$(OBJEXT)
am_cli_wvtag_OBJECTS = cli/wvtag-wvtag.$(OBJEXT) \
	cli/wvtag-utils.$(OBJEXT) cli/wvtag-import_id3.$(OBJEXT) \
	$(am__objects_9)
cli_wvtag_OBJECTS = $(am_cli_wvtag_OBJECTS)
cli_wvtag_DEPENDENCIES = src/libwavpack.la $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
cli_wvtag_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(cli_wvtag_CFLAGS) \
	$(CFLAGS) $(cli_wvtag_LDFLAGS) $(LDFLAGS) -o $@
am_cli_wvtest_OBJECTS = cli/wvtest-wvtest.$(OBJEXT) \
	cli/wvtest-md5.$(OBJEXT)
cli_wvtest_OBJECTS = $(am_cli_wvtest_OBJECTS)
cli_wvtest_DEPENDENCIES = src/libwavpack.la $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
cli_wvtest_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(cli_wvtest_CFLAGS) \
	$(CFLAGS) $(cli_wvtest_LDFLAGS) $(LDFLAGS) -o $@
am__cli_wvunpack_SOURCES_DIST = cli/wvunpack.c cli/riff_write.c \
	cli/wave64_write.c cli/caff_write.c cli/dsdiff_write.c \
	cli/aiff_write.c cli/dsf_write.c cli/utils.c cli/md5.c \
	cli/win32_unicode_support.c
#am__objects_10 = cli/wvunpack-win32_unicode_support.$(OBJEXT)
am_cli_wvunpack_OBJECTS = cli/wvunpack-wvunpack.$(OBJEXT) \
	cli/wvunpack-riff_write.$(OBJEXT) \
	cli/wvunpack-wave64_write.$(OBJEXT) \
	cli/wvunpack-caff_write.$(OBJEXT) \
	cli/wvunpack-dsdiff_write.$(OBJEXT) \
	cli/wvunpack-aiff_write.$(OBJEXT) \
	cli/wvunpack-dsf_write.$(OBJEXT) cli/wvunpack-utils.$(OBJEXT) \
	cli/wvunpack-md5.$(OBJEXT) $(am__objects_10)
cli_wvunpack_OBJECTS = $(am_cli_wvunpack_OBJECTS)
cli_wvunpack_DEPENDENCIES = src/libwavpack.la $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
cli_wvunpack_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(cli_wvunpack_CFLAGS) \
	$(CFLAGS) $(cli_wvunpack_LDFLAGS) $(LDFLAGS) -o $@
AM_V_P = $(am__v_P_$(V))
am__v_P_ = $(am__v_P_$(AM_DEFAULT_VERBOSITY))
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_$(V))
am__v_GEN_ = $(am__v_GEN_$(AM_DEFAULT_VERBOSITY))
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_$(V))
am__v_at_ = $(am__v_at_$(AM_DEFAULT_VERBOSITY))
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = cli/$(DEPDIR)/wavpack-aiff.Po \
	cli/$(DEPDIR)/wavpack-caff.Po cli/$(DEPDIR)/wavpack-dsdiff.Po \
	cli/$(DEPDIR)/wavpack-dsf.Po \
	cli/$(DEPDIR)/wavpack-import_id3.Po \
	cli/$(DEPDIR)/wavpack-md5.Po cli/$(DEPDIR)/wavpack-riff.Po \
	cli/$(DEPDIR)/wavpack-utils.Po cli/$(DEPDIR)/wavpack-wave64.Po \
	cli/$(DEPDIR)/wavpack-wavpack.Po \
	cli/$(DEPDIR)/wavpack-win32_unicode_support.Po \
	cli/$(DEPDIR)/wvgain-utils.Po \
	cli/$(DEPDIR)/wvgain-win32_unicode_support.Po \
	cli/$(DEPDIR)/wvgain-wvgain.Po \
	cli/$(DEPDIR)/wvtag-import_id3.Po cli/$(DEPDIR)/wvtag-utils.Po \
	cli/$(DEPDIR)/wvtag-win32_unicode_support.Po \
	cli/$(DEPDIR)/wvtag-wvtag.Po cli/$(DEPDIR)/wvtest-md5.Po \
	cli/$(DEPDIR)/wvtest-wvtest.Po \
	cli/$(DEPDIR)/wvunpack-aiff_write.Po \
	cli/$(DEPDIR)/wvunpack-caff_write.Po \
	cli/$(DEPDIR)/wvunpack-dsdiff_write.Po \
	cli/$(DEPDIR)/wvunpack-dsf_write.Po \
	cli/$(DEPDIR)/wvunpack-md5.Po \
	cli/$(DEPDIR)/wvunpack-riff_write.Po \
	cli/$(DEPDIR)/wvunpack-utils.Po \
	cli/$(DEPDIR)/wvunpack-wave64_write.Po \
	cli/$(DEPDIR)/wvunpack-win32_unicode_support.Po \
	cli/$(DEPDIR)/wvunpack-wvunpack.Po \
	src/$(DEPDIR)/libwavpack_la-common_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-decorr_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-entropy_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-extra1.Plo \
	src/$(DEPDIR)/libwavpack_la-extra2.Plo \
	src/$(DEPDIR)/libwavpack_la-open_filename.Plo \
	src/$(DEPDIR)/libwavpack_la-open_legacy.Plo \
	src/$(DEPDIR)/libwavpack_la-open_raw.Plo \
	src/$(DEPDIR)/libwavpack_la-open_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-pack.Plo \
	src/$(DEPDIR)/libwavpack_la-pack_dns.Plo \
	src/$(DEPDIR)/libwavpack_la-pack_dsd.Plo \
	src/$(DEPDIR)/libwavpack_la-pack_floats.Plo \
	src/$(DEPDIR)/libwavpack_la-pack_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-read_words.Plo \
	src/$(DEPDIR)/libwavpack_la-tag_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-tags.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack3.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack3_open.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack3_seek.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack_dsd.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack_floats.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack_seek.Plo \
	src/$(DEPDIR)/libwavpack_la-unpack_utils.Plo \
	src/$(DEPDIR)/libwavpack_la-write_words.Plo \
	src/$(DEPDIR)/pack_x64.Plo src/$(DEPDIR)/pack_x86.Plo \
	src/$(DEPDIR)/unpack_armv7.Plo src/$(DEPDIR)/unpack_x64.Plo \
	src/$(DEPDIR)/unpack_x86.Plo \
	wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Plo
am__mv = mv -f
CPPASCOMPILE = $(CCAS) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CCASFLAGS) $(CCASFLAGS)
LTCPPASCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CCAS) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CCASFLAGS) $(CCASFLAGS)
AM_V_CPPAS = $(am__v_CPPAS_$(V))
am__v_CPPAS_ = $(am__v_CPPAS_$(AM_DEFAULT_VERBOSITY))
am__v_CPPAS_0 = @echo "  CPPAS   " $@;
am__v_CPPAS_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_$(V))
am__v_CC_ = $(am__v_CC_$(AM_DEFAULT_VERBOSITY))
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_$(V))
am__v_CCLD_ = $(am__v_CCLD_$(AM_DEFAULT_VERBOSITY))
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(src_libwavpack_la_SOURCES) $(cli_wavpack_SOURCES) \
	$(cli_wvgain_SOURCES) $(cli_wvtag_SOURCES) \
	$(cli_wvtest_SOURCES) $(cli_wvunpack_SOURCES)
DIST_SOURCES = $(am__src_libwavpack_la_SOURCES_DIST) \
	$(am__cli_wavpack_SOURCES_DIST) $(am__cli_wvgain_SOURCES_DIST) \
	$(am__cli_wvtag_SOURCES_DIST) $(cli_wvtest_SOURCES) \
	$(am__cli_wvunpack_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
man1dir = $(mandir)/man1
NROFF = nroff
MANS = $(dist_man_MANS)
DATA = $(dist_doc_DATA) $(dist_html_DATA) $(dist_pdf_DATA) \
	$(pkgconfig_DATA)
HEADERS = $(noinst_HEADERS) $(pkginclude_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
CSCOPE = cscope
AM_RECURSIVE_TARGETS = cscope
am__tty_colors_dummy = \
  mgn= red= grn= lgn= blu= brg= std=; \
  am__color_tests=no
am__tty_colors = { \
  $(am__tty_colors_dummy); \
  if test "X$(AM_COLOR_TESTS)" = Xno; then \
    am__color_tests=no; \
  elif test "X$(AM_COLOR_TESTS)" = Xalways; then \
    am__color_tests=yes; \
  elif test "X$$TERM" != Xdumb && { test -t 1; } 2>/dev/null; then \
    am__color_tests=yes; \
  fi; \
  if test $$am__color_tests = yes; then \
    red='[0;31m'; \
    grn='[0;32m'; \
    lgn='[1;32m'; \
    blu='[1;34m'; \
    mgn='[0;35m'; \
    brg='[1m'; \
    std='[m'; \
  fi; \
}
am__DIST_COMMON = $(dist_man_MANS) $(srcdir)/Makefile.in \
	$(srcdir)/wavpack.pc.in $(top_srcdir)/cli/all-tests \
	$(top_srcdir)/cli/fast-tests AUTHORS COPYING ChangeLog NEWS \
	ar-lib compile config.guess config.rpath config.sub depcomp \
	install-sh ltmain.sh missing
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
GZIP_ENV = --best
DIST_ARCHIVES = $(distdir).tar.xz
DIST_TARGETS = dist-xz
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = ${SHELL} /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack/missing aclocal-1.16
AMTAR = $${TAR-tar}
AM_DEFAULT_VERBOSITY = 1
AR = ar
AS = as
AUTOCONF = ${SHELL} /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack/missing autoconf
AUTOHEADER = ${SHELL} /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack/missing autoheader
AUTOMAKE = ${SHELL} /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack/missing automake-1.16
AWK = gawk
CC = gcc
CCAS = gcc
CCASDEPMODE = depmode=gcc3
CCASFLAGS = -g -O2
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2 -Wall
CPP = gcc -E
CPPFLAGS = 
CYGPATH_W = echo
DEFS = -DPACKAGE_NAME=\"wavpack\" -DPACKAGE_TARNAME=\"wavpack\" -DPACKAGE_VERSION=\"5.7.0\" -DPACKAGE_STRING=\"wavpack\ 5.7.0\" -DPACKAGE_BUGREPORT=\"<EMAIL>\" -DPACKAGE_URL=\"\" -DPACKAGE=\"wavpack\" -DVERSION=\"5.7.0\" -DLIBWAVPACK_MAJOR=5 -DLIBWAVPACK_MINOR=7 -DLIBWAVPACK_MICRO=0 -DLIBWAVPACK_VERSION_STRING=\"5.7.0\" -DVERSION_OS=\"linux-gnu\" -DSTDC_HEADERS=1 -DHAVE_SYS_TYPES_H=1 -DHAVE_SYS_STAT_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRING_H=1 -DHAVE_MEMORY_H=1 -DHAVE_STRINGS_H=1 -DHAVE_INTTYPES_H=1 -DHAVE_STDINT_H=1 -DHAVE_UNISTD_H=1 -DHAVE_DLFCN_H=1 -DLT_OBJDIR=\".libs/\" -DHAVE_FSEEKO=1 -DHAVE_ICONV=1 -DICONV_CONST= -DENABLE_DSD=1 -DENABLE_THREADS=1 -DOPT_ASM_X64=1 -DHAVE___BUILTIN_CLZ=1 -DHAVE___BUILTIN_CTZ=1
DEPDIR = .deps
DLLTOOL = false
DSYMUTIL = 
DUMPBIN = 
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
FGREP = /usr/bin/grep -F
GREP = /usr/bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LD = /usr/bin/ld -m elf_x86_64
LDFLAGS = 
LIBICONV = 
LIBM = -lm
LIBOBJS = 
LIBS = 
LIBTHREAD = -lpthread
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LIPO = 
LN_S = ln -s
LTLIBICONV = 
LTLIBOBJS = 
LT_AGE = 2
LT_CURRENT = 3
LT_REVISION = 6
LT_SYS_LIBRARY_PATH = 
MAINT = 
MAKEINFO = ${SHELL} /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack/missing makeinfo
MANIFEST_TOOL = :
MKDIR_P = /usr/bin/mkdir -p
NM = /usr/bin/nm -B
NMEDIT = 
OBJDUMP = objdump
OBJEXT = o
OTOOL = 
OTOOL64 = 
PACKAGE = wavpack
PACKAGE_BUGREPORT = <EMAIL>
PACKAGE_NAME = wavpack
PACKAGE_STRING = wavpack 5.7.0
PACKAGE_TARNAME = wavpack
PACKAGE_URL = 
PACKAGE_VERSION = 5.7.0
PATH_SEPARATOR = :
RANLIB = ranlib
RC = :
SED = /usr/bin/sed
SET_MAKE = 
SHELL = /bin/sh
STRIP = strip
VERSION = 5.7.0
abs_builddir = /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack
abs_srcdir = /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack
abs_top_builddir = /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack
abs_top_srcdir = /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack
ac_ct_AR = ar
ac_ct_CC = gcc
ac_ct_DUMPBIN = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = $${TAR-tar} chof - "$$tardir"
am__untar = $${TAR-tar} xf -
bindir = ${exec_prefix}/bin
build = x86_64-pc-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = pc
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-pc-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = pc
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = ${SHELL} /home/<USER>/uw_wangxl/code/LMCompress/third_party/WavPack/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = $(MKDIR_P)
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr/local
program_transform_name = s,x,x,
psdir = ${docdir}
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target_alias = 
top_build_prefix = 
top_builddir = .
top_srcdir = .
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = wavpack.pc
dist_doc_DATA = README.md
dist_pdf_DATA = \
	doc/WavPack5FileFormat.pdf \
	doc/WavPack5LibraryDoc.pdf \
	doc/WavPack5PortingGuide.pdf

dist_html_DATA = \
	doc/wavpack_doc.html \
	doc/style.css

EXTRA_DIST = \
	CMakeLists.txt \
	cmake/CheckCLinkerFlag.cmake \
	cmake/modules/FindIconv.cmake \
	cmake/TestLargeFiles.cmake \
	\
	cli/Makefile.w32 \
	src/Makefile.os2 \
	src/Makefile.w32 \
	src/wavpack.exports \
	src/pack_x64.asm \
	src/pack_x86.asm \
	src/unpack_x64.asm \
	src/unpack_x86.asm \
	\
	src/libwavpack.vcxproj \
	wavpackexe/wavpack.vcxproj \
	wavpackdll/wavpackdll.vcxproj \
	wvgainexe/wvgain.vcxproj \
	wvunpackexe/wvunpack.vcxproj \
	wvtagexe/wvtag.vcxproj \
	wvtestexe/wvtest.vcxproj \
	wavpack.sln masm.rules

MAINTAINERCLEANFILES = \
	aclocal.m4 \
	ar-lib \
	compile \
	config.guess \
	config.rpath \
	config.sub \
	configure \
	depcomp \
	install-sh \
	ltmain.sh \
	Makefile.in \
	missing

cli_wavpack_SOURCES = cli/wavpack.c cli/riff.c cli/wave64.c cli/caff.c \
	cli/dsdiff.c cli/dsf.c cli/aiff.c cli/utils.c cli/md5.c \
	cli/import_id3.c $(am__append_1)
cli_wavpack_CFLAGS = $(AM_CFLAGS) -I$(top_srcdir)/include
cli_wavpack_LDFLAGS = -rpath $(libdir)
cli_wavpack_LDADD = $(AM_LDADD) src/libwavpack.la $(LIBM) $(LIBICONV)
cli_wvunpack_SOURCES = cli/wvunpack.c cli/riff_write.c \
	cli/wave64_write.c cli/caff_write.c cli/dsdiff_write.c \
	cli/aiff_write.c cli/dsf_write.c cli/utils.c cli/md5.c \
	$(am__append_2)
cli_wvunpack_CFLAGS = $(AM_CFLAGS) -I$(top_srcdir)/include
cli_wvunpack_LDFLAGS = -rpath $(libdir)
cli_wvunpack_LDADD = $(AM_LDADD) src/libwavpack.la $(LIBM) $(LIBICONV)
cli_wvgain_SOURCES = cli/wvgain.c cli/utils.c $(am__append_3)
cli_wvgain_CFLAGS = $(AM_CFLAGS) -I$(top_srcdir)/include
cli_wvgain_LDFLAGS = -rpath $(libdir)
cli_wvgain_LDADD = $(AM_LDADD) src/libwavpack.la $(LIBM)
cli_wvtag_SOURCES = cli/wvtag.c cli/utils.c cli/import_id3.c \
	$(am__append_4)
cli_wvtag_CFLAGS = $(AM_CFLAGS) -I$(top_srcdir)/include
cli_wvtag_LDFLAGS = -rpath $(libdir)
cli_wvtag_LDADD = $(AM_LDADD) src/libwavpack.la $(LIBM) $(LIBICONV)
cli_wvtest_SOURCES = cli/wvtest.c cli/md5.c
cli_wvtest_CFLAGS = $(AM_CFLAGS) -I$(top_srcdir)/include
cli_wvtest_LDFLAGS = -rpath $(libdir)
cli_wvtest_LDADD = $(AM_LDADD) src/libwavpack.la $(LIBM) $(LIBTHREAD)
TESTS = cli/fast-tests
TESTS_ENVIRONMENT = $(SHELL)
noinst_HEADERS = \
	cli/win32_unicode_support.h \
	cli/utils.h \
	cli/md5.h


############
# include/ #
############
pkginclude_HEADERS = include/wavpack.h

########
# man/ #
########
dist_man_MANS = \
	man/wavpack.1 \
	man/wvgain.1 \
	man/wvunpack.1 \
	man/wvtag.1


########
# src/ #
########
lib_LTLIBRARIES = src/libwavpack.la
src_libwavpack_la_SOURCES = src/common_utils.c src/decorr_utils.c \
	src/entropy_utils.c src/extra1.c src/extra2.c src/open_utils.c \
	src/open_filename.c src/open_legacy.c src/open_raw.c \
	src/pack.c src/pack_dns.c src/pack_floats.c src/pack_utils.c \
	src/read_words.c src/tags.c src/tag_utils.c src/unpack.c \
	src/unpack_floats.c src/unpack_seek.c src/unpack_utils.c \
	src/write_words.c src/decorr_tables.h src/unpack3.h \
	src/wavpack_local.h src/wavpack_version.h $(am__append_5) \
	$(am__append_6) $(am__append_7) $(am__append_8) \
	$(am__append_9) $(am__append_10)
##src_libwavpack_la_DEPENDENCIES = wavpackdll/wavpackdll.o
##win32res_link = -Wl,wavpackdll/wavpackdll.o
src_libwavpack_la_CFLAGS = $(AM_CFLAGS) -I$(top_srcdir)/include
src_libwavpack_la_LIBADD = $(AM_LDADD) $(LIBM) $(LIBTHREAD)
src_libwavpack_la_LDFLAGS = -version-info $(LT_CURRENT):$(LT_REVISION):$(LT_AGE) $(win32res_link) -export-symbols-regex '^Wavpack.*$$' -no-undefined
all: all-am

.SUFFIXES:
.SUFFIXES: .S .c .lo .o .obj .rc
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --foreign \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure:  $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):
wavpack.pc: $(top_builddir)/config.status $(srcdir)/wavpack.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-checkPROGRAMS:
	@list='$(check_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
src/$(am__dirstamp):
	@$(MKDIR_P) src
	@: > src/$(am__dirstamp)
src/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/$(DEPDIR)
	@: > src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-common_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-decorr_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-entropy_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-extra1.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-extra2.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-open_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-open_filename.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-open_legacy.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-open_raw.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-pack.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-pack_dns.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-pack_floats.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-pack_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-read_words.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-tags.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-tag_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack_floats.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack_seek.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack_utils.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-write_words.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack3.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack3_open.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack3_seek.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-pack_dsd.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/libwavpack_la-unpack_dsd.lo: src/$(am__dirstamp) \
	src/$(DEPDIR)/$(am__dirstamp)
src/pack_x86.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/unpack_x86.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/pack_x64.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/unpack_x64.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/unpack_armv7.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
wavpackdll/$(am__dirstamp):
	@$(MKDIR_P) wavpackdll
	@: > wavpackdll/$(am__dirstamp)
wavpackdll/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) wavpackdll/$(DEPDIR)
	@: > wavpackdll/$(DEPDIR)/$(am__dirstamp)
wavpackdll/src_libwavpack_la-dummy.lo: wavpackdll/$(am__dirstamp) \
	wavpackdll/$(DEPDIR)/$(am__dirstamp)

src/libwavpack.la: $(src_libwavpack_la_OBJECTS) $(src_libwavpack_la_DEPENDENCIES) $(EXTRA_src_libwavpack_la_DEPENDENCIES) src/$(am__dirstamp)
	$(AM_V_CCLD)$(src_libwavpack_la_LINK) -rpath $(libdir) $(src_libwavpack_la_OBJECTS) $(src_libwavpack_la_LIBADD) $(LIBS)
cli/$(am__dirstamp):
	@$(MKDIR_P) cli
	@: > cli/$(am__dirstamp)
cli/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) cli/$(DEPDIR)
	@: > cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-wavpack.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-riff.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-wave64.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-caff.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-dsdiff.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-dsf.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-aiff.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-utils.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-md5.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-import_id3.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wavpack-win32_unicode_support.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)

cli/wavpack$(EXEEXT): $(cli_wavpack_OBJECTS) $(cli_wavpack_DEPENDENCIES) $(EXTRA_cli_wavpack_DEPENDENCIES) cli/$(am__dirstamp)
	@rm -f cli/wavpack$(EXEEXT)
	$(AM_V_CCLD)$(cli_wavpack_LINK) $(cli_wavpack_OBJECTS) $(cli_wavpack_LDADD) $(LIBS)
cli/wvgain-wvgain.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvgain-utils.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvgain-win32_unicode_support.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)

cli/wvgain$(EXEEXT): $(cli_wvgain_OBJECTS) $(cli_wvgain_DEPENDENCIES) $(EXTRA_cli_wvgain_DEPENDENCIES) cli/$(am__dirstamp)
	@rm -f cli/wvgain$(EXEEXT)
	$(AM_V_CCLD)$(cli_wvgain_LINK) $(cli_wvgain_OBJECTS) $(cli_wvgain_LDADD) $(LIBS)
cli/wvtag-wvtag.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvtag-utils.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvtag-import_id3.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvtag-win32_unicode_support.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)

cli/wvtag$(EXEEXT): $(cli_wvtag_OBJECTS) $(cli_wvtag_DEPENDENCIES) $(EXTRA_cli_wvtag_DEPENDENCIES) cli/$(am__dirstamp)
	@rm -f cli/wvtag$(EXEEXT)
	$(AM_V_CCLD)$(cli_wvtag_LINK) $(cli_wvtag_OBJECTS) $(cli_wvtag_LDADD) $(LIBS)
cli/wvtest-wvtest.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvtest-md5.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)

cli/wvtest$(EXEEXT): $(cli_wvtest_OBJECTS) $(cli_wvtest_DEPENDENCIES) $(EXTRA_cli_wvtest_DEPENDENCIES) cli/$(am__dirstamp)
	@rm -f cli/wvtest$(EXEEXT)
	$(AM_V_CCLD)$(cli_wvtest_LINK) $(cli_wvtest_OBJECTS) $(cli_wvtest_LDADD) $(LIBS)
cli/wvunpack-wvunpack.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-riff_write.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-wave64_write.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-caff_write.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-dsdiff_write.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-aiff_write.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-dsf_write.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-utils.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-md5.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)
cli/wvunpack-win32_unicode_support.$(OBJEXT): cli/$(am__dirstamp) \
	cli/$(DEPDIR)/$(am__dirstamp)

cli/wvunpack$(EXEEXT): $(cli_wvunpack_OBJECTS) $(cli_wvunpack_DEPENDENCIES) $(EXTRA_cli_wvunpack_DEPENDENCIES) cli/$(am__dirstamp)
	@rm -f cli/wvunpack$(EXEEXT)
	$(AM_V_CCLD)$(cli_wvunpack_LINK) $(cli_wvunpack_OBJECTS) $(cli_wvunpack_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f cli/*.$(OBJEXT)
	-rm -f src/*.$(OBJEXT)
	-rm -f src/*.lo
	-rm -f wavpackdll/*.$(OBJEXT)
	-rm -f wavpackdll/*.lo

distclean-compile:
	-rm -f *.tab.c

include cli/$(DEPDIR)/wavpack-aiff.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-caff.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-dsdiff.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-dsf.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-import_id3.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-md5.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-riff.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-utils.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-wave64.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-wavpack.Po # am--include-marker
include cli/$(DEPDIR)/wavpack-win32_unicode_support.Po # am--include-marker
include cli/$(DEPDIR)/wvgain-utils.Po # am--include-marker
include cli/$(DEPDIR)/wvgain-win32_unicode_support.Po # am--include-marker
include cli/$(DEPDIR)/wvgain-wvgain.Po # am--include-marker
include cli/$(DEPDIR)/wvtag-import_id3.Po # am--include-marker
include cli/$(DEPDIR)/wvtag-utils.Po # am--include-marker
include cli/$(DEPDIR)/wvtag-win32_unicode_support.Po # am--include-marker
include cli/$(DEPDIR)/wvtag-wvtag.Po # am--include-marker
include cli/$(DEPDIR)/wvtest-md5.Po # am--include-marker
include cli/$(DEPDIR)/wvtest-wvtest.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-aiff_write.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-caff_write.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-dsdiff_write.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-dsf_write.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-md5.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-riff_write.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-utils.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-wave64_write.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-win32_unicode_support.Po # am--include-marker
include cli/$(DEPDIR)/wvunpack-wvunpack.Po # am--include-marker
include src/$(DEPDIR)/libwavpack_la-common_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-decorr_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-entropy_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-extra1.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-extra2.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-open_filename.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-open_legacy.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-open_raw.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-open_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-pack.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-pack_dns.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-pack_dsd.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-pack_floats.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-pack_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-read_words.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-tag_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-tags.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack3.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack3_open.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack3_seek.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack_dsd.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack_floats.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack_seek.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-unpack_utils.Plo # am--include-marker
include src/$(DEPDIR)/libwavpack_la-write_words.Plo # am--include-marker
include src/$(DEPDIR)/pack_x64.Plo # am--include-marker
include src/$(DEPDIR)/pack_x86.Plo # am--include-marker
include src/$(DEPDIR)/unpack_armv7.Plo # am--include-marker
include src/$(DEPDIR)/unpack_x64.Plo # am--include-marker
include src/$(DEPDIR)/unpack_x86.Plo # am--include-marker
include wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Plo # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.S.o:
	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CPPAS)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) \
#	$(AM_V_CPPAS_no)$(CPPASCOMPILE) -c -o $@ $<

.S.obj:
	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CPPAS)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) \
#	$(AM_V_CPPAS_no)$(CPPASCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.S.lo:
	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
	$(LTCPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CPPAS)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) \
#	$(AM_V_CPPAS_no)$(LTCPPASCOMPILE) -c -o $@ $<

.c.o:
	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ $<

.c.obj:
	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
	$(am__mv) $$depbase.Tpo $$depbase.Po
#	$(AM_V_CC)source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
	$(am__mv) $$depbase.Tpo $$depbase.Plo
#	$(AM_V_CC)source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LTCOMPILE) -c -o $@ $<

src/libwavpack_la-common_utils.lo: src/common_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-common_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-common_utils.Tpo -c -o src/libwavpack_la-common_utils.lo `test -f 'src/common_utils.c' || echo '$(srcdir)/'`src/common_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-common_utils.Tpo src/$(DEPDIR)/libwavpack_la-common_utils.Plo
#	$(AM_V_CC)source='src/common_utils.c' object='src/libwavpack_la-common_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-common_utils.lo `test -f 'src/common_utils.c' || echo '$(srcdir)/'`src/common_utils.c

src/libwavpack_la-decorr_utils.lo: src/decorr_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-decorr_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-decorr_utils.Tpo -c -o src/libwavpack_la-decorr_utils.lo `test -f 'src/decorr_utils.c' || echo '$(srcdir)/'`src/decorr_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-decorr_utils.Tpo src/$(DEPDIR)/libwavpack_la-decorr_utils.Plo
#	$(AM_V_CC)source='src/decorr_utils.c' object='src/libwavpack_la-decorr_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-decorr_utils.lo `test -f 'src/decorr_utils.c' || echo '$(srcdir)/'`src/decorr_utils.c

src/libwavpack_la-entropy_utils.lo: src/entropy_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-entropy_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-entropy_utils.Tpo -c -o src/libwavpack_la-entropy_utils.lo `test -f 'src/entropy_utils.c' || echo '$(srcdir)/'`src/entropy_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-entropy_utils.Tpo src/$(DEPDIR)/libwavpack_la-entropy_utils.Plo
#	$(AM_V_CC)source='src/entropy_utils.c' object='src/libwavpack_la-entropy_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-entropy_utils.lo `test -f 'src/entropy_utils.c' || echo '$(srcdir)/'`src/entropy_utils.c

src/libwavpack_la-extra1.lo: src/extra1.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-extra1.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-extra1.Tpo -c -o src/libwavpack_la-extra1.lo `test -f 'src/extra1.c' || echo '$(srcdir)/'`src/extra1.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-extra1.Tpo src/$(DEPDIR)/libwavpack_la-extra1.Plo
#	$(AM_V_CC)source='src/extra1.c' object='src/libwavpack_la-extra1.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-extra1.lo `test -f 'src/extra1.c' || echo '$(srcdir)/'`src/extra1.c

src/libwavpack_la-extra2.lo: src/extra2.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-extra2.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-extra2.Tpo -c -o src/libwavpack_la-extra2.lo `test -f 'src/extra2.c' || echo '$(srcdir)/'`src/extra2.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-extra2.Tpo src/$(DEPDIR)/libwavpack_la-extra2.Plo
#	$(AM_V_CC)source='src/extra2.c' object='src/libwavpack_la-extra2.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-extra2.lo `test -f 'src/extra2.c' || echo '$(srcdir)/'`src/extra2.c

src/libwavpack_la-open_utils.lo: src/open_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-open_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-open_utils.Tpo -c -o src/libwavpack_la-open_utils.lo `test -f 'src/open_utils.c' || echo '$(srcdir)/'`src/open_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-open_utils.Tpo src/$(DEPDIR)/libwavpack_la-open_utils.Plo
#	$(AM_V_CC)source='src/open_utils.c' object='src/libwavpack_la-open_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-open_utils.lo `test -f 'src/open_utils.c' || echo '$(srcdir)/'`src/open_utils.c

src/libwavpack_la-open_filename.lo: src/open_filename.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-open_filename.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-open_filename.Tpo -c -o src/libwavpack_la-open_filename.lo `test -f 'src/open_filename.c' || echo '$(srcdir)/'`src/open_filename.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-open_filename.Tpo src/$(DEPDIR)/libwavpack_la-open_filename.Plo
#	$(AM_V_CC)source='src/open_filename.c' object='src/libwavpack_la-open_filename.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-open_filename.lo `test -f 'src/open_filename.c' || echo '$(srcdir)/'`src/open_filename.c

src/libwavpack_la-open_legacy.lo: src/open_legacy.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-open_legacy.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-open_legacy.Tpo -c -o src/libwavpack_la-open_legacy.lo `test -f 'src/open_legacy.c' || echo '$(srcdir)/'`src/open_legacy.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-open_legacy.Tpo src/$(DEPDIR)/libwavpack_la-open_legacy.Plo
#	$(AM_V_CC)source='src/open_legacy.c' object='src/libwavpack_la-open_legacy.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-open_legacy.lo `test -f 'src/open_legacy.c' || echo '$(srcdir)/'`src/open_legacy.c

src/libwavpack_la-open_raw.lo: src/open_raw.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-open_raw.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-open_raw.Tpo -c -o src/libwavpack_la-open_raw.lo `test -f 'src/open_raw.c' || echo '$(srcdir)/'`src/open_raw.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-open_raw.Tpo src/$(DEPDIR)/libwavpack_la-open_raw.Plo
#	$(AM_V_CC)source='src/open_raw.c' object='src/libwavpack_la-open_raw.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-open_raw.lo `test -f 'src/open_raw.c' || echo '$(srcdir)/'`src/open_raw.c

src/libwavpack_la-pack.lo: src/pack.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-pack.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-pack.Tpo -c -o src/libwavpack_la-pack.lo `test -f 'src/pack.c' || echo '$(srcdir)/'`src/pack.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-pack.Tpo src/$(DEPDIR)/libwavpack_la-pack.Plo
#	$(AM_V_CC)source='src/pack.c' object='src/libwavpack_la-pack.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-pack.lo `test -f 'src/pack.c' || echo '$(srcdir)/'`src/pack.c

src/libwavpack_la-pack_dns.lo: src/pack_dns.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-pack_dns.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-pack_dns.Tpo -c -o src/libwavpack_la-pack_dns.lo `test -f 'src/pack_dns.c' || echo '$(srcdir)/'`src/pack_dns.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-pack_dns.Tpo src/$(DEPDIR)/libwavpack_la-pack_dns.Plo
#	$(AM_V_CC)source='src/pack_dns.c' object='src/libwavpack_la-pack_dns.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-pack_dns.lo `test -f 'src/pack_dns.c' || echo '$(srcdir)/'`src/pack_dns.c

src/libwavpack_la-pack_floats.lo: src/pack_floats.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-pack_floats.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-pack_floats.Tpo -c -o src/libwavpack_la-pack_floats.lo `test -f 'src/pack_floats.c' || echo '$(srcdir)/'`src/pack_floats.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-pack_floats.Tpo src/$(DEPDIR)/libwavpack_la-pack_floats.Plo
#	$(AM_V_CC)source='src/pack_floats.c' object='src/libwavpack_la-pack_floats.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-pack_floats.lo `test -f 'src/pack_floats.c' || echo '$(srcdir)/'`src/pack_floats.c

src/libwavpack_la-pack_utils.lo: src/pack_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-pack_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-pack_utils.Tpo -c -o src/libwavpack_la-pack_utils.lo `test -f 'src/pack_utils.c' || echo '$(srcdir)/'`src/pack_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-pack_utils.Tpo src/$(DEPDIR)/libwavpack_la-pack_utils.Plo
#	$(AM_V_CC)source='src/pack_utils.c' object='src/libwavpack_la-pack_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-pack_utils.lo `test -f 'src/pack_utils.c' || echo '$(srcdir)/'`src/pack_utils.c

src/libwavpack_la-read_words.lo: src/read_words.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-read_words.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-read_words.Tpo -c -o src/libwavpack_la-read_words.lo `test -f 'src/read_words.c' || echo '$(srcdir)/'`src/read_words.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-read_words.Tpo src/$(DEPDIR)/libwavpack_la-read_words.Plo
#	$(AM_V_CC)source='src/read_words.c' object='src/libwavpack_la-read_words.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-read_words.lo `test -f 'src/read_words.c' || echo '$(srcdir)/'`src/read_words.c

src/libwavpack_la-tags.lo: src/tags.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-tags.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-tags.Tpo -c -o src/libwavpack_la-tags.lo `test -f 'src/tags.c' || echo '$(srcdir)/'`src/tags.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-tags.Tpo src/$(DEPDIR)/libwavpack_la-tags.Plo
#	$(AM_V_CC)source='src/tags.c' object='src/libwavpack_la-tags.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-tags.lo `test -f 'src/tags.c' || echo '$(srcdir)/'`src/tags.c

src/libwavpack_la-tag_utils.lo: src/tag_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-tag_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-tag_utils.Tpo -c -o src/libwavpack_la-tag_utils.lo `test -f 'src/tag_utils.c' || echo '$(srcdir)/'`src/tag_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-tag_utils.Tpo src/$(DEPDIR)/libwavpack_la-tag_utils.Plo
#	$(AM_V_CC)source='src/tag_utils.c' object='src/libwavpack_la-tag_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-tag_utils.lo `test -f 'src/tag_utils.c' || echo '$(srcdir)/'`src/tag_utils.c

src/libwavpack_la-unpack.lo: src/unpack.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack.Tpo -c -o src/libwavpack_la-unpack.lo `test -f 'src/unpack.c' || echo '$(srcdir)/'`src/unpack.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack.Tpo src/$(DEPDIR)/libwavpack_la-unpack.Plo
#	$(AM_V_CC)source='src/unpack.c' object='src/libwavpack_la-unpack.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack.lo `test -f 'src/unpack.c' || echo '$(srcdir)/'`src/unpack.c

src/libwavpack_la-unpack_floats.lo: src/unpack_floats.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack_floats.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack_floats.Tpo -c -o src/libwavpack_la-unpack_floats.lo `test -f 'src/unpack_floats.c' || echo '$(srcdir)/'`src/unpack_floats.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack_floats.Tpo src/$(DEPDIR)/libwavpack_la-unpack_floats.Plo
#	$(AM_V_CC)source='src/unpack_floats.c' object='src/libwavpack_la-unpack_floats.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack_floats.lo `test -f 'src/unpack_floats.c' || echo '$(srcdir)/'`src/unpack_floats.c

src/libwavpack_la-unpack_seek.lo: src/unpack_seek.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack_seek.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack_seek.Tpo -c -o src/libwavpack_la-unpack_seek.lo `test -f 'src/unpack_seek.c' || echo '$(srcdir)/'`src/unpack_seek.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack_seek.Tpo src/$(DEPDIR)/libwavpack_la-unpack_seek.Plo
#	$(AM_V_CC)source='src/unpack_seek.c' object='src/libwavpack_la-unpack_seek.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack_seek.lo `test -f 'src/unpack_seek.c' || echo '$(srcdir)/'`src/unpack_seek.c

src/libwavpack_la-unpack_utils.lo: src/unpack_utils.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack_utils.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack_utils.Tpo -c -o src/libwavpack_la-unpack_utils.lo `test -f 'src/unpack_utils.c' || echo '$(srcdir)/'`src/unpack_utils.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack_utils.Tpo src/$(DEPDIR)/libwavpack_la-unpack_utils.Plo
#	$(AM_V_CC)source='src/unpack_utils.c' object='src/libwavpack_la-unpack_utils.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack_utils.lo `test -f 'src/unpack_utils.c' || echo '$(srcdir)/'`src/unpack_utils.c

src/libwavpack_la-write_words.lo: src/write_words.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-write_words.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-write_words.Tpo -c -o src/libwavpack_la-write_words.lo `test -f 'src/write_words.c' || echo '$(srcdir)/'`src/write_words.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-write_words.Tpo src/$(DEPDIR)/libwavpack_la-write_words.Plo
#	$(AM_V_CC)source='src/write_words.c' object='src/libwavpack_la-write_words.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-write_words.lo `test -f 'src/write_words.c' || echo '$(srcdir)/'`src/write_words.c

src/libwavpack_la-unpack3.lo: src/unpack3.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack3.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack3.Tpo -c -o src/libwavpack_la-unpack3.lo `test -f 'src/unpack3.c' || echo '$(srcdir)/'`src/unpack3.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack3.Tpo src/$(DEPDIR)/libwavpack_la-unpack3.Plo
#	$(AM_V_CC)source='src/unpack3.c' object='src/libwavpack_la-unpack3.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack3.lo `test -f 'src/unpack3.c' || echo '$(srcdir)/'`src/unpack3.c

src/libwavpack_la-unpack3_open.lo: src/unpack3_open.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack3_open.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack3_open.Tpo -c -o src/libwavpack_la-unpack3_open.lo `test -f 'src/unpack3_open.c' || echo '$(srcdir)/'`src/unpack3_open.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack3_open.Tpo src/$(DEPDIR)/libwavpack_la-unpack3_open.Plo
#	$(AM_V_CC)source='src/unpack3_open.c' object='src/libwavpack_la-unpack3_open.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack3_open.lo `test -f 'src/unpack3_open.c' || echo '$(srcdir)/'`src/unpack3_open.c

src/libwavpack_la-unpack3_seek.lo: src/unpack3_seek.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack3_seek.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack3_seek.Tpo -c -o src/libwavpack_la-unpack3_seek.lo `test -f 'src/unpack3_seek.c' || echo '$(srcdir)/'`src/unpack3_seek.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack3_seek.Tpo src/$(DEPDIR)/libwavpack_la-unpack3_seek.Plo
#	$(AM_V_CC)source='src/unpack3_seek.c' object='src/libwavpack_la-unpack3_seek.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack3_seek.lo `test -f 'src/unpack3_seek.c' || echo '$(srcdir)/'`src/unpack3_seek.c

src/libwavpack_la-pack_dsd.lo: src/pack_dsd.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-pack_dsd.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-pack_dsd.Tpo -c -o src/libwavpack_la-pack_dsd.lo `test -f 'src/pack_dsd.c' || echo '$(srcdir)/'`src/pack_dsd.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-pack_dsd.Tpo src/$(DEPDIR)/libwavpack_la-pack_dsd.Plo
#	$(AM_V_CC)source='src/pack_dsd.c' object='src/libwavpack_la-pack_dsd.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-pack_dsd.lo `test -f 'src/pack_dsd.c' || echo '$(srcdir)/'`src/pack_dsd.c

src/libwavpack_la-unpack_dsd.lo: src/unpack_dsd.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT src/libwavpack_la-unpack_dsd.lo -MD -MP -MF src/$(DEPDIR)/libwavpack_la-unpack_dsd.Tpo -c -o src/libwavpack_la-unpack_dsd.lo `test -f 'src/unpack_dsd.c' || echo '$(srcdir)/'`src/unpack_dsd.c
	$(AM_V_at)$(am__mv) src/$(DEPDIR)/libwavpack_la-unpack_dsd.Tpo src/$(DEPDIR)/libwavpack_la-unpack_dsd.Plo
#	$(AM_V_CC)source='src/unpack_dsd.c' object='src/libwavpack_la-unpack_dsd.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o src/libwavpack_la-unpack_dsd.lo `test -f 'src/unpack_dsd.c' || echo '$(srcdir)/'`src/unpack_dsd.c

wavpackdll/src_libwavpack_la-dummy.lo: wavpackdll/dummy.c
	$(AM_V_CC)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -MT wavpackdll/src_libwavpack_la-dummy.lo -MD -MP -MF wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Tpo -c -o wavpackdll/src_libwavpack_la-dummy.lo `test -f 'wavpackdll/dummy.c' || echo '$(srcdir)/'`wavpackdll/dummy.c
	$(AM_V_at)$(am__mv) wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Tpo wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Plo
#	$(AM_V_CC)source='wavpackdll/dummy.c' object='wavpackdll/src_libwavpack_la-dummy.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(src_libwavpack_la_CFLAGS) $(CFLAGS) -c -o wavpackdll/src_libwavpack_la-dummy.lo `test -f 'wavpackdll/dummy.c' || echo '$(srcdir)/'`wavpackdll/dummy.c

cli/wavpack-wavpack.o: cli/wavpack.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-wavpack.o -MD -MP -MF cli/$(DEPDIR)/wavpack-wavpack.Tpo -c -o cli/wavpack-wavpack.o `test -f 'cli/wavpack.c' || echo '$(srcdir)/'`cli/wavpack.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-wavpack.Tpo cli/$(DEPDIR)/wavpack-wavpack.Po
#	$(AM_V_CC)source='cli/wavpack.c' object='cli/wavpack-wavpack.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-wavpack.o `test -f 'cli/wavpack.c' || echo '$(srcdir)/'`cli/wavpack.c

cli/wavpack-wavpack.obj: cli/wavpack.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-wavpack.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-wavpack.Tpo -c -o cli/wavpack-wavpack.obj `if test -f 'cli/wavpack.c'; then $(CYGPATH_W) 'cli/wavpack.c'; else $(CYGPATH_W) '$(srcdir)/cli/wavpack.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-wavpack.Tpo cli/$(DEPDIR)/wavpack-wavpack.Po
#	$(AM_V_CC)source='cli/wavpack.c' object='cli/wavpack-wavpack.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-wavpack.obj `if test -f 'cli/wavpack.c'; then $(CYGPATH_W) 'cli/wavpack.c'; else $(CYGPATH_W) '$(srcdir)/cli/wavpack.c'; fi`

cli/wavpack-riff.o: cli/riff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-riff.o -MD -MP -MF cli/$(DEPDIR)/wavpack-riff.Tpo -c -o cli/wavpack-riff.o `test -f 'cli/riff.c' || echo '$(srcdir)/'`cli/riff.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-riff.Tpo cli/$(DEPDIR)/wavpack-riff.Po
#	$(AM_V_CC)source='cli/riff.c' object='cli/wavpack-riff.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-riff.o `test -f 'cli/riff.c' || echo '$(srcdir)/'`cli/riff.c

cli/wavpack-riff.obj: cli/riff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-riff.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-riff.Tpo -c -o cli/wavpack-riff.obj `if test -f 'cli/riff.c'; then $(CYGPATH_W) 'cli/riff.c'; else $(CYGPATH_W) '$(srcdir)/cli/riff.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-riff.Tpo cli/$(DEPDIR)/wavpack-riff.Po
#	$(AM_V_CC)source='cli/riff.c' object='cli/wavpack-riff.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-riff.obj `if test -f 'cli/riff.c'; then $(CYGPATH_W) 'cli/riff.c'; else $(CYGPATH_W) '$(srcdir)/cli/riff.c'; fi`

cli/wavpack-wave64.o: cli/wave64.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-wave64.o -MD -MP -MF cli/$(DEPDIR)/wavpack-wave64.Tpo -c -o cli/wavpack-wave64.o `test -f 'cli/wave64.c' || echo '$(srcdir)/'`cli/wave64.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-wave64.Tpo cli/$(DEPDIR)/wavpack-wave64.Po
#	$(AM_V_CC)source='cli/wave64.c' object='cli/wavpack-wave64.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-wave64.o `test -f 'cli/wave64.c' || echo '$(srcdir)/'`cli/wave64.c

cli/wavpack-wave64.obj: cli/wave64.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-wave64.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-wave64.Tpo -c -o cli/wavpack-wave64.obj `if test -f 'cli/wave64.c'; then $(CYGPATH_W) 'cli/wave64.c'; else $(CYGPATH_W) '$(srcdir)/cli/wave64.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-wave64.Tpo cli/$(DEPDIR)/wavpack-wave64.Po
#	$(AM_V_CC)source='cli/wave64.c' object='cli/wavpack-wave64.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-wave64.obj `if test -f 'cli/wave64.c'; then $(CYGPATH_W) 'cli/wave64.c'; else $(CYGPATH_W) '$(srcdir)/cli/wave64.c'; fi`

cli/wavpack-caff.o: cli/caff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-caff.o -MD -MP -MF cli/$(DEPDIR)/wavpack-caff.Tpo -c -o cli/wavpack-caff.o `test -f 'cli/caff.c' || echo '$(srcdir)/'`cli/caff.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-caff.Tpo cli/$(DEPDIR)/wavpack-caff.Po
#	$(AM_V_CC)source='cli/caff.c' object='cli/wavpack-caff.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-caff.o `test -f 'cli/caff.c' || echo '$(srcdir)/'`cli/caff.c

cli/wavpack-caff.obj: cli/caff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-caff.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-caff.Tpo -c -o cli/wavpack-caff.obj `if test -f 'cli/caff.c'; then $(CYGPATH_W) 'cli/caff.c'; else $(CYGPATH_W) '$(srcdir)/cli/caff.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-caff.Tpo cli/$(DEPDIR)/wavpack-caff.Po
#	$(AM_V_CC)source='cli/caff.c' object='cli/wavpack-caff.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-caff.obj `if test -f 'cli/caff.c'; then $(CYGPATH_W) 'cli/caff.c'; else $(CYGPATH_W) '$(srcdir)/cli/caff.c'; fi`

cli/wavpack-dsdiff.o: cli/dsdiff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-dsdiff.o -MD -MP -MF cli/$(DEPDIR)/wavpack-dsdiff.Tpo -c -o cli/wavpack-dsdiff.o `test -f 'cli/dsdiff.c' || echo '$(srcdir)/'`cli/dsdiff.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-dsdiff.Tpo cli/$(DEPDIR)/wavpack-dsdiff.Po
#	$(AM_V_CC)source='cli/dsdiff.c' object='cli/wavpack-dsdiff.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-dsdiff.o `test -f 'cli/dsdiff.c' || echo '$(srcdir)/'`cli/dsdiff.c

cli/wavpack-dsdiff.obj: cli/dsdiff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-dsdiff.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-dsdiff.Tpo -c -o cli/wavpack-dsdiff.obj `if test -f 'cli/dsdiff.c'; then $(CYGPATH_W) 'cli/dsdiff.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsdiff.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-dsdiff.Tpo cli/$(DEPDIR)/wavpack-dsdiff.Po
#	$(AM_V_CC)source='cli/dsdiff.c' object='cli/wavpack-dsdiff.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-dsdiff.obj `if test -f 'cli/dsdiff.c'; then $(CYGPATH_W) 'cli/dsdiff.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsdiff.c'; fi`

cli/wavpack-dsf.o: cli/dsf.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-dsf.o -MD -MP -MF cli/$(DEPDIR)/wavpack-dsf.Tpo -c -o cli/wavpack-dsf.o `test -f 'cli/dsf.c' || echo '$(srcdir)/'`cli/dsf.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-dsf.Tpo cli/$(DEPDIR)/wavpack-dsf.Po
#	$(AM_V_CC)source='cli/dsf.c' object='cli/wavpack-dsf.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-dsf.o `test -f 'cli/dsf.c' || echo '$(srcdir)/'`cli/dsf.c

cli/wavpack-dsf.obj: cli/dsf.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-dsf.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-dsf.Tpo -c -o cli/wavpack-dsf.obj `if test -f 'cli/dsf.c'; then $(CYGPATH_W) 'cli/dsf.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsf.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-dsf.Tpo cli/$(DEPDIR)/wavpack-dsf.Po
#	$(AM_V_CC)source='cli/dsf.c' object='cli/wavpack-dsf.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-dsf.obj `if test -f 'cli/dsf.c'; then $(CYGPATH_W) 'cli/dsf.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsf.c'; fi`

cli/wavpack-aiff.o: cli/aiff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-aiff.o -MD -MP -MF cli/$(DEPDIR)/wavpack-aiff.Tpo -c -o cli/wavpack-aiff.o `test -f 'cli/aiff.c' || echo '$(srcdir)/'`cli/aiff.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-aiff.Tpo cli/$(DEPDIR)/wavpack-aiff.Po
#	$(AM_V_CC)source='cli/aiff.c' object='cli/wavpack-aiff.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-aiff.o `test -f 'cli/aiff.c' || echo '$(srcdir)/'`cli/aiff.c

cli/wavpack-aiff.obj: cli/aiff.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-aiff.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-aiff.Tpo -c -o cli/wavpack-aiff.obj `if test -f 'cli/aiff.c'; then $(CYGPATH_W) 'cli/aiff.c'; else $(CYGPATH_W) '$(srcdir)/cli/aiff.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-aiff.Tpo cli/$(DEPDIR)/wavpack-aiff.Po
#	$(AM_V_CC)source='cli/aiff.c' object='cli/wavpack-aiff.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-aiff.obj `if test -f 'cli/aiff.c'; then $(CYGPATH_W) 'cli/aiff.c'; else $(CYGPATH_W) '$(srcdir)/cli/aiff.c'; fi`

cli/wavpack-utils.o: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-utils.o -MD -MP -MF cli/$(DEPDIR)/wavpack-utils.Tpo -c -o cli/wavpack-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-utils.Tpo cli/$(DEPDIR)/wavpack-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wavpack-utils.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c

cli/wavpack-utils.obj: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-utils.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-utils.Tpo -c -o cli/wavpack-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-utils.Tpo cli/$(DEPDIR)/wavpack-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wavpack-utils.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`

cli/wavpack-md5.o: cli/md5.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-md5.o -MD -MP -MF cli/$(DEPDIR)/wavpack-md5.Tpo -c -o cli/wavpack-md5.o `test -f 'cli/md5.c' || echo '$(srcdir)/'`cli/md5.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-md5.Tpo cli/$(DEPDIR)/wavpack-md5.Po
#	$(AM_V_CC)source='cli/md5.c' object='cli/wavpack-md5.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-md5.o `test -f 'cli/md5.c' || echo '$(srcdir)/'`cli/md5.c

cli/wavpack-md5.obj: cli/md5.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-md5.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-md5.Tpo -c -o cli/wavpack-md5.obj `if test -f 'cli/md5.c'; then $(CYGPATH_W) 'cli/md5.c'; else $(CYGPATH_W) '$(srcdir)/cli/md5.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-md5.Tpo cli/$(DEPDIR)/wavpack-md5.Po
#	$(AM_V_CC)source='cli/md5.c' object='cli/wavpack-md5.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-md5.obj `if test -f 'cli/md5.c'; then $(CYGPATH_W) 'cli/md5.c'; else $(CYGPATH_W) '$(srcdir)/cli/md5.c'; fi`

cli/wavpack-import_id3.o: cli/import_id3.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-import_id3.o -MD -MP -MF cli/$(DEPDIR)/wavpack-import_id3.Tpo -c -o cli/wavpack-import_id3.o `test -f 'cli/import_id3.c' || echo '$(srcdir)/'`cli/import_id3.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-import_id3.Tpo cli/$(DEPDIR)/wavpack-import_id3.Po
#	$(AM_V_CC)source='cli/import_id3.c' object='cli/wavpack-import_id3.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-import_id3.o `test -f 'cli/import_id3.c' || echo '$(srcdir)/'`cli/import_id3.c

cli/wavpack-import_id3.obj: cli/import_id3.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-import_id3.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-import_id3.Tpo -c -o cli/wavpack-import_id3.obj `if test -f 'cli/import_id3.c'; then $(CYGPATH_W) 'cli/import_id3.c'; else $(CYGPATH_W) '$(srcdir)/cli/import_id3.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-import_id3.Tpo cli/$(DEPDIR)/wavpack-import_id3.Po
#	$(AM_V_CC)source='cli/import_id3.c' object='cli/wavpack-import_id3.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-import_id3.obj `if test -f 'cli/import_id3.c'; then $(CYGPATH_W) 'cli/import_id3.c'; else $(CYGPATH_W) '$(srcdir)/cli/import_id3.c'; fi`

cli/wavpack-win32_unicode_support.o: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-win32_unicode_support.o -MD -MP -MF cli/$(DEPDIR)/wavpack-win32_unicode_support.Tpo -c -o cli/wavpack-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-win32_unicode_support.Tpo cli/$(DEPDIR)/wavpack-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wavpack-win32_unicode_support.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c

cli/wavpack-win32_unicode_support.obj: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -MT cli/wavpack-win32_unicode_support.obj -MD -MP -MF cli/$(DEPDIR)/wavpack-win32_unicode_support.Tpo -c -o cli/wavpack-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wavpack-win32_unicode_support.Tpo cli/$(DEPDIR)/wavpack-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wavpack-win32_unicode_support.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wavpack_CFLAGS) $(CFLAGS) -c -o cli/wavpack-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`

cli/wvgain-wvgain.o: cli/wvgain.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -MT cli/wvgain-wvgain.o -MD -MP -MF cli/$(DEPDIR)/wvgain-wvgain.Tpo -c -o cli/wvgain-wvgain.o `test -f 'cli/wvgain.c' || echo '$(srcdir)/'`cli/wvgain.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvgain-wvgain.Tpo cli/$(DEPDIR)/wvgain-wvgain.Po
#	$(AM_V_CC)source='cli/wvgain.c' object='cli/wvgain-wvgain.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -c -o cli/wvgain-wvgain.o `test -f 'cli/wvgain.c' || echo '$(srcdir)/'`cli/wvgain.c

cli/wvgain-wvgain.obj: cli/wvgain.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -MT cli/wvgain-wvgain.obj -MD -MP -MF cli/$(DEPDIR)/wvgain-wvgain.Tpo -c -o cli/wvgain-wvgain.obj `if test -f 'cli/wvgain.c'; then $(CYGPATH_W) 'cli/wvgain.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvgain.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvgain-wvgain.Tpo cli/$(DEPDIR)/wvgain-wvgain.Po
#	$(AM_V_CC)source='cli/wvgain.c' object='cli/wvgain-wvgain.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -c -o cli/wvgain-wvgain.obj `if test -f 'cli/wvgain.c'; then $(CYGPATH_W) 'cli/wvgain.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvgain.c'; fi`

cli/wvgain-utils.o: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -MT cli/wvgain-utils.o -MD -MP -MF cli/$(DEPDIR)/wvgain-utils.Tpo -c -o cli/wvgain-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvgain-utils.Tpo cli/$(DEPDIR)/wvgain-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wvgain-utils.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -c -o cli/wvgain-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c

cli/wvgain-utils.obj: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -MT cli/wvgain-utils.obj -MD -MP -MF cli/$(DEPDIR)/wvgain-utils.Tpo -c -o cli/wvgain-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvgain-utils.Tpo cli/$(DEPDIR)/wvgain-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wvgain-utils.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -c -o cli/wvgain-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`

cli/wvgain-win32_unicode_support.o: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -MT cli/wvgain-win32_unicode_support.o -MD -MP -MF cli/$(DEPDIR)/wvgain-win32_unicode_support.Tpo -c -o cli/wvgain-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvgain-win32_unicode_support.Tpo cli/$(DEPDIR)/wvgain-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wvgain-win32_unicode_support.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -c -o cli/wvgain-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c

cli/wvgain-win32_unicode_support.obj: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -MT cli/wvgain-win32_unicode_support.obj -MD -MP -MF cli/$(DEPDIR)/wvgain-win32_unicode_support.Tpo -c -o cli/wvgain-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvgain-win32_unicode_support.Tpo cli/$(DEPDIR)/wvgain-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wvgain-win32_unicode_support.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvgain_CFLAGS) $(CFLAGS) -c -o cli/wvgain-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`

cli/wvtag-wvtag.o: cli/wvtag.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-wvtag.o -MD -MP -MF cli/$(DEPDIR)/wvtag-wvtag.Tpo -c -o cli/wvtag-wvtag.o `test -f 'cli/wvtag.c' || echo '$(srcdir)/'`cli/wvtag.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-wvtag.Tpo cli/$(DEPDIR)/wvtag-wvtag.Po
#	$(AM_V_CC)source='cli/wvtag.c' object='cli/wvtag-wvtag.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-wvtag.o `test -f 'cli/wvtag.c' || echo '$(srcdir)/'`cli/wvtag.c

cli/wvtag-wvtag.obj: cli/wvtag.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-wvtag.obj -MD -MP -MF cli/$(DEPDIR)/wvtag-wvtag.Tpo -c -o cli/wvtag-wvtag.obj `if test -f 'cli/wvtag.c'; then $(CYGPATH_W) 'cli/wvtag.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvtag.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-wvtag.Tpo cli/$(DEPDIR)/wvtag-wvtag.Po
#	$(AM_V_CC)source='cli/wvtag.c' object='cli/wvtag-wvtag.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-wvtag.obj `if test -f 'cli/wvtag.c'; then $(CYGPATH_W) 'cli/wvtag.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvtag.c'; fi`

cli/wvtag-utils.o: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-utils.o -MD -MP -MF cli/$(DEPDIR)/wvtag-utils.Tpo -c -o cli/wvtag-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-utils.Tpo cli/$(DEPDIR)/wvtag-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wvtag-utils.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c

cli/wvtag-utils.obj: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-utils.obj -MD -MP -MF cli/$(DEPDIR)/wvtag-utils.Tpo -c -o cli/wvtag-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-utils.Tpo cli/$(DEPDIR)/wvtag-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wvtag-utils.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`

cli/wvtag-import_id3.o: cli/import_id3.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-import_id3.o -MD -MP -MF cli/$(DEPDIR)/wvtag-import_id3.Tpo -c -o cli/wvtag-import_id3.o `test -f 'cli/import_id3.c' || echo '$(srcdir)/'`cli/import_id3.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-import_id3.Tpo cli/$(DEPDIR)/wvtag-import_id3.Po
#	$(AM_V_CC)source='cli/import_id3.c' object='cli/wvtag-import_id3.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-import_id3.o `test -f 'cli/import_id3.c' || echo '$(srcdir)/'`cli/import_id3.c

cli/wvtag-import_id3.obj: cli/import_id3.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-import_id3.obj -MD -MP -MF cli/$(DEPDIR)/wvtag-import_id3.Tpo -c -o cli/wvtag-import_id3.obj `if test -f 'cli/import_id3.c'; then $(CYGPATH_W) 'cli/import_id3.c'; else $(CYGPATH_W) '$(srcdir)/cli/import_id3.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-import_id3.Tpo cli/$(DEPDIR)/wvtag-import_id3.Po
#	$(AM_V_CC)source='cli/import_id3.c' object='cli/wvtag-import_id3.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-import_id3.obj `if test -f 'cli/import_id3.c'; then $(CYGPATH_W) 'cli/import_id3.c'; else $(CYGPATH_W) '$(srcdir)/cli/import_id3.c'; fi`

cli/wvtag-win32_unicode_support.o: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-win32_unicode_support.o -MD -MP -MF cli/$(DEPDIR)/wvtag-win32_unicode_support.Tpo -c -o cli/wvtag-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-win32_unicode_support.Tpo cli/$(DEPDIR)/wvtag-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wvtag-win32_unicode_support.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c

cli/wvtag-win32_unicode_support.obj: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -MT cli/wvtag-win32_unicode_support.obj -MD -MP -MF cli/$(DEPDIR)/wvtag-win32_unicode_support.Tpo -c -o cli/wvtag-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtag-win32_unicode_support.Tpo cli/$(DEPDIR)/wvtag-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wvtag-win32_unicode_support.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtag_CFLAGS) $(CFLAGS) -c -o cli/wvtag-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`

cli/wvtest-wvtest.o: cli/wvtest.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -MT cli/wvtest-wvtest.o -MD -MP -MF cli/$(DEPDIR)/wvtest-wvtest.Tpo -c -o cli/wvtest-wvtest.o `test -f 'cli/wvtest.c' || echo '$(srcdir)/'`cli/wvtest.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtest-wvtest.Tpo cli/$(DEPDIR)/wvtest-wvtest.Po
#	$(AM_V_CC)source='cli/wvtest.c' object='cli/wvtest-wvtest.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -c -o cli/wvtest-wvtest.o `test -f 'cli/wvtest.c' || echo '$(srcdir)/'`cli/wvtest.c

cli/wvtest-wvtest.obj: cli/wvtest.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -MT cli/wvtest-wvtest.obj -MD -MP -MF cli/$(DEPDIR)/wvtest-wvtest.Tpo -c -o cli/wvtest-wvtest.obj `if test -f 'cli/wvtest.c'; then $(CYGPATH_W) 'cli/wvtest.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvtest.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtest-wvtest.Tpo cli/$(DEPDIR)/wvtest-wvtest.Po
#	$(AM_V_CC)source='cli/wvtest.c' object='cli/wvtest-wvtest.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -c -o cli/wvtest-wvtest.obj `if test -f 'cli/wvtest.c'; then $(CYGPATH_W) 'cli/wvtest.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvtest.c'; fi`

cli/wvtest-md5.o: cli/md5.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -MT cli/wvtest-md5.o -MD -MP -MF cli/$(DEPDIR)/wvtest-md5.Tpo -c -o cli/wvtest-md5.o `test -f 'cli/md5.c' || echo '$(srcdir)/'`cli/md5.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtest-md5.Tpo cli/$(DEPDIR)/wvtest-md5.Po
#	$(AM_V_CC)source='cli/md5.c' object='cli/wvtest-md5.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -c -o cli/wvtest-md5.o `test -f 'cli/md5.c' || echo '$(srcdir)/'`cli/md5.c

cli/wvtest-md5.obj: cli/md5.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -MT cli/wvtest-md5.obj -MD -MP -MF cli/$(DEPDIR)/wvtest-md5.Tpo -c -o cli/wvtest-md5.obj `if test -f 'cli/md5.c'; then $(CYGPATH_W) 'cli/md5.c'; else $(CYGPATH_W) '$(srcdir)/cli/md5.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvtest-md5.Tpo cli/$(DEPDIR)/wvtest-md5.Po
#	$(AM_V_CC)source='cli/md5.c' object='cli/wvtest-md5.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvtest_CFLAGS) $(CFLAGS) -c -o cli/wvtest-md5.obj `if test -f 'cli/md5.c'; then $(CYGPATH_W) 'cli/md5.c'; else $(CYGPATH_W) '$(srcdir)/cli/md5.c'; fi`

cli/wvunpack-wvunpack.o: cli/wvunpack.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-wvunpack.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-wvunpack.Tpo -c -o cli/wvunpack-wvunpack.o `test -f 'cli/wvunpack.c' || echo '$(srcdir)/'`cli/wvunpack.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-wvunpack.Tpo cli/$(DEPDIR)/wvunpack-wvunpack.Po
#	$(AM_V_CC)source='cli/wvunpack.c' object='cli/wvunpack-wvunpack.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-wvunpack.o `test -f 'cli/wvunpack.c' || echo '$(srcdir)/'`cli/wvunpack.c

cli/wvunpack-wvunpack.obj: cli/wvunpack.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-wvunpack.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-wvunpack.Tpo -c -o cli/wvunpack-wvunpack.obj `if test -f 'cli/wvunpack.c'; then $(CYGPATH_W) 'cli/wvunpack.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvunpack.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-wvunpack.Tpo cli/$(DEPDIR)/wvunpack-wvunpack.Po
#	$(AM_V_CC)source='cli/wvunpack.c' object='cli/wvunpack-wvunpack.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-wvunpack.obj `if test -f 'cli/wvunpack.c'; then $(CYGPATH_W) 'cli/wvunpack.c'; else $(CYGPATH_W) '$(srcdir)/cli/wvunpack.c'; fi`

cli/wvunpack-riff_write.o: cli/riff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-riff_write.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-riff_write.Tpo -c -o cli/wvunpack-riff_write.o `test -f 'cli/riff_write.c' || echo '$(srcdir)/'`cli/riff_write.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-riff_write.Tpo cli/$(DEPDIR)/wvunpack-riff_write.Po
#	$(AM_V_CC)source='cli/riff_write.c' object='cli/wvunpack-riff_write.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-riff_write.o `test -f 'cli/riff_write.c' || echo '$(srcdir)/'`cli/riff_write.c

cli/wvunpack-riff_write.obj: cli/riff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-riff_write.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-riff_write.Tpo -c -o cli/wvunpack-riff_write.obj `if test -f 'cli/riff_write.c'; then $(CYGPATH_W) 'cli/riff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/riff_write.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-riff_write.Tpo cli/$(DEPDIR)/wvunpack-riff_write.Po
#	$(AM_V_CC)source='cli/riff_write.c' object='cli/wvunpack-riff_write.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-riff_write.obj `if test -f 'cli/riff_write.c'; then $(CYGPATH_W) 'cli/riff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/riff_write.c'; fi`

cli/wvunpack-wave64_write.o: cli/wave64_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-wave64_write.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-wave64_write.Tpo -c -o cli/wvunpack-wave64_write.o `test -f 'cli/wave64_write.c' || echo '$(srcdir)/'`cli/wave64_write.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-wave64_write.Tpo cli/$(DEPDIR)/wvunpack-wave64_write.Po
#	$(AM_V_CC)source='cli/wave64_write.c' object='cli/wvunpack-wave64_write.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-wave64_write.o `test -f 'cli/wave64_write.c' || echo '$(srcdir)/'`cli/wave64_write.c

cli/wvunpack-wave64_write.obj: cli/wave64_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-wave64_write.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-wave64_write.Tpo -c -o cli/wvunpack-wave64_write.obj `if test -f 'cli/wave64_write.c'; then $(CYGPATH_W) 'cli/wave64_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/wave64_write.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-wave64_write.Tpo cli/$(DEPDIR)/wvunpack-wave64_write.Po
#	$(AM_V_CC)source='cli/wave64_write.c' object='cli/wvunpack-wave64_write.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-wave64_write.obj `if test -f 'cli/wave64_write.c'; then $(CYGPATH_W) 'cli/wave64_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/wave64_write.c'; fi`

cli/wvunpack-caff_write.o: cli/caff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-caff_write.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-caff_write.Tpo -c -o cli/wvunpack-caff_write.o `test -f 'cli/caff_write.c' || echo '$(srcdir)/'`cli/caff_write.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-caff_write.Tpo cli/$(DEPDIR)/wvunpack-caff_write.Po
#	$(AM_V_CC)source='cli/caff_write.c' object='cli/wvunpack-caff_write.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-caff_write.o `test -f 'cli/caff_write.c' || echo '$(srcdir)/'`cli/caff_write.c

cli/wvunpack-caff_write.obj: cli/caff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-caff_write.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-caff_write.Tpo -c -o cli/wvunpack-caff_write.obj `if test -f 'cli/caff_write.c'; then $(CYGPATH_W) 'cli/caff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/caff_write.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-caff_write.Tpo cli/$(DEPDIR)/wvunpack-caff_write.Po
#	$(AM_V_CC)source='cli/caff_write.c' object='cli/wvunpack-caff_write.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-caff_write.obj `if test -f 'cli/caff_write.c'; then $(CYGPATH_W) 'cli/caff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/caff_write.c'; fi`

cli/wvunpack-dsdiff_write.o: cli/dsdiff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-dsdiff_write.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-dsdiff_write.Tpo -c -o cli/wvunpack-dsdiff_write.o `test -f 'cli/dsdiff_write.c' || echo '$(srcdir)/'`cli/dsdiff_write.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-dsdiff_write.Tpo cli/$(DEPDIR)/wvunpack-dsdiff_write.Po
#	$(AM_V_CC)source='cli/dsdiff_write.c' object='cli/wvunpack-dsdiff_write.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-dsdiff_write.o `test -f 'cli/dsdiff_write.c' || echo '$(srcdir)/'`cli/dsdiff_write.c

cli/wvunpack-dsdiff_write.obj: cli/dsdiff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-dsdiff_write.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-dsdiff_write.Tpo -c -o cli/wvunpack-dsdiff_write.obj `if test -f 'cli/dsdiff_write.c'; then $(CYGPATH_W) 'cli/dsdiff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsdiff_write.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-dsdiff_write.Tpo cli/$(DEPDIR)/wvunpack-dsdiff_write.Po
#	$(AM_V_CC)source='cli/dsdiff_write.c' object='cli/wvunpack-dsdiff_write.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-dsdiff_write.obj `if test -f 'cli/dsdiff_write.c'; then $(CYGPATH_W) 'cli/dsdiff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsdiff_write.c'; fi`

cli/wvunpack-aiff_write.o: cli/aiff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-aiff_write.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-aiff_write.Tpo -c -o cli/wvunpack-aiff_write.o `test -f 'cli/aiff_write.c' || echo '$(srcdir)/'`cli/aiff_write.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-aiff_write.Tpo cli/$(DEPDIR)/wvunpack-aiff_write.Po
#	$(AM_V_CC)source='cli/aiff_write.c' object='cli/wvunpack-aiff_write.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-aiff_write.o `test -f 'cli/aiff_write.c' || echo '$(srcdir)/'`cli/aiff_write.c

cli/wvunpack-aiff_write.obj: cli/aiff_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-aiff_write.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-aiff_write.Tpo -c -o cli/wvunpack-aiff_write.obj `if test -f 'cli/aiff_write.c'; then $(CYGPATH_W) 'cli/aiff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/aiff_write.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-aiff_write.Tpo cli/$(DEPDIR)/wvunpack-aiff_write.Po
#	$(AM_V_CC)source='cli/aiff_write.c' object='cli/wvunpack-aiff_write.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-aiff_write.obj `if test -f 'cli/aiff_write.c'; then $(CYGPATH_W) 'cli/aiff_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/aiff_write.c'; fi`

cli/wvunpack-dsf_write.o: cli/dsf_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-dsf_write.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-dsf_write.Tpo -c -o cli/wvunpack-dsf_write.o `test -f 'cli/dsf_write.c' || echo '$(srcdir)/'`cli/dsf_write.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-dsf_write.Tpo cli/$(DEPDIR)/wvunpack-dsf_write.Po
#	$(AM_V_CC)source='cli/dsf_write.c' object='cli/wvunpack-dsf_write.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-dsf_write.o `test -f 'cli/dsf_write.c' || echo '$(srcdir)/'`cli/dsf_write.c

cli/wvunpack-dsf_write.obj: cli/dsf_write.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-dsf_write.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-dsf_write.Tpo -c -o cli/wvunpack-dsf_write.obj `if test -f 'cli/dsf_write.c'; then $(CYGPATH_W) 'cli/dsf_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsf_write.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-dsf_write.Tpo cli/$(DEPDIR)/wvunpack-dsf_write.Po
#	$(AM_V_CC)source='cli/dsf_write.c' object='cli/wvunpack-dsf_write.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-dsf_write.obj `if test -f 'cli/dsf_write.c'; then $(CYGPATH_W) 'cli/dsf_write.c'; else $(CYGPATH_W) '$(srcdir)/cli/dsf_write.c'; fi`

cli/wvunpack-utils.o: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-utils.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-utils.Tpo -c -o cli/wvunpack-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-utils.Tpo cli/$(DEPDIR)/wvunpack-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wvunpack-utils.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-utils.o `test -f 'cli/utils.c' || echo '$(srcdir)/'`cli/utils.c

cli/wvunpack-utils.obj: cli/utils.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-utils.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-utils.Tpo -c -o cli/wvunpack-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-utils.Tpo cli/$(DEPDIR)/wvunpack-utils.Po
#	$(AM_V_CC)source='cli/utils.c' object='cli/wvunpack-utils.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-utils.obj `if test -f 'cli/utils.c'; then $(CYGPATH_W) 'cli/utils.c'; else $(CYGPATH_W) '$(srcdir)/cli/utils.c'; fi`

cli/wvunpack-md5.o: cli/md5.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-md5.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-md5.Tpo -c -o cli/wvunpack-md5.o `test -f 'cli/md5.c' || echo '$(srcdir)/'`cli/md5.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-md5.Tpo cli/$(DEPDIR)/wvunpack-md5.Po
#	$(AM_V_CC)source='cli/md5.c' object='cli/wvunpack-md5.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-md5.o `test -f 'cli/md5.c' || echo '$(srcdir)/'`cli/md5.c

cli/wvunpack-md5.obj: cli/md5.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-md5.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-md5.Tpo -c -o cli/wvunpack-md5.obj `if test -f 'cli/md5.c'; then $(CYGPATH_W) 'cli/md5.c'; else $(CYGPATH_W) '$(srcdir)/cli/md5.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-md5.Tpo cli/$(DEPDIR)/wvunpack-md5.Po
#	$(AM_V_CC)source='cli/md5.c' object='cli/wvunpack-md5.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-md5.obj `if test -f 'cli/md5.c'; then $(CYGPATH_W) 'cli/md5.c'; else $(CYGPATH_W) '$(srcdir)/cli/md5.c'; fi`

cli/wvunpack-win32_unicode_support.o: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-win32_unicode_support.o -MD -MP -MF cli/$(DEPDIR)/wvunpack-win32_unicode_support.Tpo -c -o cli/wvunpack-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-win32_unicode_support.Tpo cli/$(DEPDIR)/wvunpack-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wvunpack-win32_unicode_support.o' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-win32_unicode_support.o `test -f 'cli/win32_unicode_support.c' || echo '$(srcdir)/'`cli/win32_unicode_support.c

cli/wvunpack-win32_unicode_support.obj: cli/win32_unicode_support.c
	$(AM_V_CC)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -MT cli/wvunpack-win32_unicode_support.obj -MD -MP -MF cli/$(DEPDIR)/wvunpack-win32_unicode_support.Tpo -c -o cli/wvunpack-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`
	$(AM_V_at)$(am__mv) cli/$(DEPDIR)/wvunpack-win32_unicode_support.Tpo cli/$(DEPDIR)/wvunpack-win32_unicode_support.Po
#	$(AM_V_CC)source='cli/win32_unicode_support.c' object='cli/wvunpack-win32_unicode_support.obj' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(AM_V_CC_no)$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(cli_wvunpack_CFLAGS) $(CFLAGS) -c -o cli/wvunpack-win32_unicode_support.obj `if test -f 'cli/win32_unicode_support.c'; then $(CYGPATH_W) 'cli/win32_unicode_support.c'; else $(CYGPATH_W) '$(srcdir)/cli/win32_unicode_support.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf cli/.libs cli/_libs
	-rm -rf src/.libs src/_libs
	-rm -rf wavpackdll/.libs wavpackdll/_libs

distclean-libtool:
	-rm -f libtool config.lt
install-man1: $(dist_man_MANS)
	@$(NORMAL_INSTALL)
	@list1=''; \
	list2='$(dist_man_MANS)'; \
	test -n "$(man1dir)" \
	  && test -n "`echo $$list1$$list2`" \
	  || exit 0; \
	echo " $(MKDIR_P) '$(DESTDIR)$(man1dir)'"; \
	$(MKDIR_P) "$(DESTDIR)$(man1dir)" || exit 1; \
	{ for i in $$list1; do echo "$$i"; done;  \
	if test -n "$$list2"; then \
	  for i in $$list2; do echo "$$i"; done \
	    | sed -n '/\.1[a-z]*$$/p'; \
	fi; \
	} | while read p; do \
	  if test -f $$p; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; echo "$$p"; \
	done | \
	sed -e 'n;s,.*/,,;p;h;s,.*\.,,;s,^[^1][0-9a-z]*$$,1,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,' | \
	sed 'N;N;s,\n, ,g' | { \
	list=; while read file base inst; do \
	  if test "$$base" = "$$inst"; then list="$$list $$file"; else \
	    echo " $(INSTALL_DATA) '$$file' '$(DESTDIR)$(man1dir)/$$inst'"; \
	    $(INSTALL_DATA) "$$file" "$(DESTDIR)$(man1dir)/$$inst" || exit $$?; \
	  fi; \
	done; \
	for i in $$list; do echo "$$i"; done | $(am__base_list) | \
	while read files; do \
	  test -z "$$files" || { \
	    echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(man1dir)'"; \
	    $(INSTALL_DATA) $$files "$(DESTDIR)$(man1dir)" || exit $$?; }; \
	done; }

uninstall-man1:
	@$(NORMAL_UNINSTALL)
	@list=''; test -n "$(man1dir)" || exit 0; \
	files=`{ for i in $$list; do echo "$$i"; done; \
	l2='$(dist_man_MANS)'; for i in $$l2; do echo "$$i"; done | \
	  sed -n '/\.1[a-z]*$$/p'; \
	} | sed -e 's,.*/,,;h;s,.*\.,,;s,^[^1][0-9a-z]*$$,1,;x' \
	      -e 's,\.[0-9a-z]*$$,,;$(transform);G;s,\n,.,'`; \
	dir='$(DESTDIR)$(man1dir)'; $(am__uninstall_files_from_dir)
install-dist_docDATA: $(dist_doc_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_doc_DATA)'; test -n "$(docdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(docdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(docdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(docdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(docdir)" || exit $$?; \
	done

uninstall-dist_docDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_doc_DATA)'; test -n "$(docdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(docdir)'; $(am__uninstall_files_from_dir)
install-dist_htmlDATA: $(dist_html_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_html_DATA)'; test -n "$(htmldir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(htmldir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(htmldir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(htmldir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(htmldir)" || exit $$?; \
	done

uninstall-dist_htmlDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_html_DATA)'; test -n "$(htmldir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(htmldir)'; $(am__uninstall_files_from_dir)
install-dist_pdfDATA: $(dist_pdf_DATA)
	@$(NORMAL_INSTALL)
	@list='$(dist_pdf_DATA)'; test -n "$(pdfdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pdfdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pdfdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pdfdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pdfdir)" || exit $$?; \
	done

uninstall-dist_pdfDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(dist_pdf_DATA)'; test -n "$(pdfdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pdfdir)'; $(am__uninstall_files_from_dir)
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)
install-pkgincludeHEADERS: $(pkginclude_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgincludedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgincludedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(pkgincludedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(pkgincludedir)" || exit $$?; \
	done

uninstall-pkgincludeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(pkginclude_HEADERS)'; test -n "$(pkgincludedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgincludedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files

check-TESTS: $(TESTS)
	@failed=0; all=0; xfail=0; xpass=0; skip=0; \
	srcdir=$(srcdir); export srcdir; \
	list=' $(TESTS) '; \
	$(am__tty_colors); \
	if test -n "$$list"; then \
	  for tst in $$list; do \
	    if test -f ./$$tst; then dir=./; \
	    elif test -f $$tst; then dir=; \
	    else dir="$(srcdir)/"; fi; \
	    if $(TESTS_ENVIRONMENT) $${dir}$$tst $(AM_TESTS_FD_REDIRECT); then \
	      all=`expr $$all + 1`; \
	      case " $(XFAIL_TESTS) " in \
	      *[\ \	]$$tst[\ \	]*) \
		xpass=`expr $$xpass + 1`; \
		failed=`expr $$failed + 1`; \
		col=$$red; res=XPASS; \
	      ;; \
	      *) \
		col=$$grn; res=PASS; \
	      ;; \
	      esac; \
	    elif test $$? -ne 77; then \
	      all=`expr $$all + 1`; \
	      case " $(XFAIL_TESTS) " in \
	      *[\ \	]$$tst[\ \	]*) \
		xfail=`expr $$xfail + 1`; \
		col=$$lgn; res=XFAIL; \
	      ;; \
	      *) \
		failed=`expr $$failed + 1`; \
		col=$$red; res=FAIL; \
	      ;; \
	      esac; \
	    else \
	      skip=`expr $$skip + 1`; \
	      col=$$blu; res=SKIP; \
	    fi; \
	    echo "$${col}$$res$${std}: $$tst"; \
	  done; \
	  if test "$$all" -eq 1; then \
	    tests="test"; \
	    All=""; \
	  else \
	    tests="tests"; \
	    All="All "; \
	  fi; \
	  if test "$$failed" -eq 0; then \
	    if test "$$xfail" -eq 0; then \
	      banner="$$All$$all $$tests passed"; \
	    else \
	      if test "$$xfail" -eq 1; then failures=failure; else failures=failures; fi; \
	      banner="$$All$$all $$tests behaved as expected ($$xfail expected $$failures)"; \
	    fi; \
	  else \
	    if test "$$xpass" -eq 0; then \
	      banner="$$failed of $$all $$tests failed"; \
	    else \
	      if test "$$xpass" -eq 1; then passes=pass; else passes=passes; fi; \
	      banner="$$failed of $$all $$tests did not behave as expected ($$xpass unexpected $$passes)"; \
	    fi; \
	  fi; \
	  dashes="$$banner"; \
	  skipped=""; \
	  if test "$$skip" -ne 0; then \
	    if test "$$skip" -eq 1; then \
	      skipped="($$skip test was not run)"; \
	    else \
	      skipped="($$skip tests were not run)"; \
	    fi; \
	    test `echo "$$skipped" | wc -c` -le `echo "$$banner" | wc -c` || \
	      dashes="$$skipped"; \
	  fi; \
	  report=""; \
	  if test "$$failed" -ne 0 && test -n "$(PACKAGE_BUGREPORT)"; then \
	    report="Please report to $(PACKAGE_BUGREPORT)"; \
	    test `echo "$$report" | wc -c` -le `echo "$$banner" | wc -c` || \
	      dashes="$$report"; \
	  fi; \
	  dashes=`echo "$$dashes" | sed s/./=/g`; \
	  if test "$$failed" -eq 0; then \
	    col="$$grn"; \
	  else \
	    col="$$red"; \
	  fi; \
	  echo "$${col}$$dashes$${std}"; \
	  echo "$${col}$$banner$${std}"; \
	  test -z "$$skipped" || echo "$${col}$$skipped$${std}"; \
	  test -z "$$report" || echo "$${col}$$report$${std}"; \
	  echo "$${col}$$dashes$${std}"; \
	  test "$$failed" -eq 0; \
	else :; fi

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)
dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-zstd: distdir
	tardir=$(distdir) && $(am__tar) | zstd -c $${ZSTD_CLEVEL-$${ZSTD_OPT--19}} >$(distdir).tar.zst
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	*.tar.zst*) \
	  zstd -dc $(distdir).tar.zst | $(am__untar) ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) dvi \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) $(check_PROGRAMS)
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: check-am
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(MANS) $(DATA) $(HEADERS)
install-binPROGRAMS: install-libLTLIBRARIES

installdirs:
	for dir in "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" "$(DESTDIR)$(man1dir)" "$(DESTDIR)$(docdir)" "$(DESTDIR)$(htmldir)" "$(DESTDIR)$(pdfdir)" "$(DESTDIR)$(pkgconfigdir)" "$(DESTDIR)$(pkgincludedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f cli/$(DEPDIR)/$(am__dirstamp)
	-rm -f cli/$(am__dirstamp)
	-rm -f src/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/$(am__dirstamp)
	-rm -f wavpackdll/$(DEPDIR)/$(am__dirstamp)
	-rm -f wavpackdll/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(MAINTAINERCLEANFILES)" || rm -f $(MAINTAINERCLEANFILES)
clean: clean-am

clean-am: clean-binPROGRAMS clean-checkPROGRAMS clean-generic \
	clean-libLTLIBRARIES clean-libtool mostlyclean-am

distclean: distclean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
		-rm -f cli/$(DEPDIR)/wavpack-aiff.Po
	-rm -f cli/$(DEPDIR)/wavpack-caff.Po
	-rm -f cli/$(DEPDIR)/wavpack-dsdiff.Po
	-rm -f cli/$(DEPDIR)/wavpack-dsf.Po
	-rm -f cli/$(DEPDIR)/wavpack-import_id3.Po
	-rm -f cli/$(DEPDIR)/wavpack-md5.Po
	-rm -f cli/$(DEPDIR)/wavpack-riff.Po
	-rm -f cli/$(DEPDIR)/wavpack-utils.Po
	-rm -f cli/$(DEPDIR)/wavpack-wave64.Po
	-rm -f cli/$(DEPDIR)/wavpack-wavpack.Po
	-rm -f cli/$(DEPDIR)/wavpack-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvgain-utils.Po
	-rm -f cli/$(DEPDIR)/wvgain-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvgain-wvgain.Po
	-rm -f cli/$(DEPDIR)/wvtag-import_id3.Po
	-rm -f cli/$(DEPDIR)/wvtag-utils.Po
	-rm -f cli/$(DEPDIR)/wvtag-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvtag-wvtag.Po
	-rm -f cli/$(DEPDIR)/wvtest-md5.Po
	-rm -f cli/$(DEPDIR)/wvtest-wvtest.Po
	-rm -f cli/$(DEPDIR)/wvunpack-aiff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-caff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-dsdiff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-dsf_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-md5.Po
	-rm -f cli/$(DEPDIR)/wvunpack-riff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-utils.Po
	-rm -f cli/$(DEPDIR)/wvunpack-wave64_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvunpack-wvunpack.Po
	-rm -f src/$(DEPDIR)/libwavpack_la-common_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-decorr_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-entropy_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-extra1.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-extra2.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_filename.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_legacy.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_raw.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_dns.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_dsd.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_floats.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-read_words.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-tag_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-tags.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack3.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack3_open.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack3_seek.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_dsd.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_floats.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_seek.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-write_words.Plo
	-rm -f src/$(DEPDIR)/pack_x64.Plo
	-rm -f src/$(DEPDIR)/pack_x86.Plo
	-rm -f src/$(DEPDIR)/unpack_armv7.Plo
	-rm -f src/$(DEPDIR)/unpack_x64.Plo
	-rm -f src/$(DEPDIR)/unpack_x86.Plo
	-rm -f wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-libtool distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-dist_docDATA install-dist_htmlDATA \
	install-dist_pdfDATA install-man install-pkgconfigDATA \
	install-pkgincludeHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man: install-man1

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
		-rm -f cli/$(DEPDIR)/wavpack-aiff.Po
	-rm -f cli/$(DEPDIR)/wavpack-caff.Po
	-rm -f cli/$(DEPDIR)/wavpack-dsdiff.Po
	-rm -f cli/$(DEPDIR)/wavpack-dsf.Po
	-rm -f cli/$(DEPDIR)/wavpack-import_id3.Po
	-rm -f cli/$(DEPDIR)/wavpack-md5.Po
	-rm -f cli/$(DEPDIR)/wavpack-riff.Po
	-rm -f cli/$(DEPDIR)/wavpack-utils.Po
	-rm -f cli/$(DEPDIR)/wavpack-wave64.Po
	-rm -f cli/$(DEPDIR)/wavpack-wavpack.Po
	-rm -f cli/$(DEPDIR)/wavpack-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvgain-utils.Po
	-rm -f cli/$(DEPDIR)/wvgain-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvgain-wvgain.Po
	-rm -f cli/$(DEPDIR)/wvtag-import_id3.Po
	-rm -f cli/$(DEPDIR)/wvtag-utils.Po
	-rm -f cli/$(DEPDIR)/wvtag-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvtag-wvtag.Po
	-rm -f cli/$(DEPDIR)/wvtest-md5.Po
	-rm -f cli/$(DEPDIR)/wvtest-wvtest.Po
	-rm -f cli/$(DEPDIR)/wvunpack-aiff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-caff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-dsdiff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-dsf_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-md5.Po
	-rm -f cli/$(DEPDIR)/wvunpack-riff_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-utils.Po
	-rm -f cli/$(DEPDIR)/wvunpack-wave64_write.Po
	-rm -f cli/$(DEPDIR)/wvunpack-win32_unicode_support.Po
	-rm -f cli/$(DEPDIR)/wvunpack-wvunpack.Po
	-rm -f src/$(DEPDIR)/libwavpack_la-common_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-decorr_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-entropy_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-extra1.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-extra2.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_filename.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_legacy.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_raw.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-open_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_dns.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_dsd.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_floats.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-pack_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-read_words.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-tag_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-tags.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack3.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack3_open.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack3_seek.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_dsd.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_floats.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_seek.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-unpack_utils.Plo
	-rm -f src/$(DEPDIR)/libwavpack_la-write_words.Plo
	-rm -f src/$(DEPDIR)/pack_x64.Plo
	-rm -f src/$(DEPDIR)/pack_x86.Plo
	-rm -f src/$(DEPDIR)/unpack_armv7.Plo
	-rm -f src/$(DEPDIR)/unpack_x64.Plo
	-rm -f src/$(DEPDIR)/unpack_x86.Plo
	-rm -f wavpackdll/$(DEPDIR)/src_libwavpack_la-dummy.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-dist_docDATA \
	uninstall-dist_htmlDATA uninstall-dist_pdfDATA \
	uninstall-libLTLIBRARIES uninstall-man uninstall-pkgconfigDATA \
	uninstall-pkgincludeHEADERS

uninstall-man: uninstall-man1

.MAKE: check-am install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles am--refresh check \
	check-TESTS check-am clean clean-binPROGRAMS \
	clean-checkPROGRAMS clean-cscope clean-generic \
	clean-libLTLIBRARIES clean-libtool cscope cscopelist-am ctags \
	ctags-am dist dist-all dist-bzip2 dist-gzip dist-lzip \
	dist-shar dist-tarZ dist-xz dist-zip dist-zstd distcheck \
	distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distcleancheck distdir \
	distuninstallcheck dvi dvi-am html html-am info info-am \
	install install-am install-binPROGRAMS install-data \
	install-data-am install-dist_docDATA install-dist_htmlDATA \
	install-dist_pdfDATA install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-libLTLIBRARIES install-man \
	install-man1 install-pdf install-pdf-am install-pkgconfigDATA \
	install-pkgincludeHEADERS install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am \
	uninstall-binPROGRAMS uninstall-dist_docDATA \
	uninstall-dist_htmlDATA uninstall-dist_pdfDATA \
	uninstall-libLTLIBRARIES uninstall-man uninstall-man1 \
	uninstall-pkgconfigDATA uninstall-pkgincludeHEADERS

.PRECIOUS: Makefile

##wavpackdll/wavpackdll.o: wavpackdll/wavpackdll.rc

.rc.o:
	$(RC) $(AM_CPPFLAGS) -i $< $@

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
