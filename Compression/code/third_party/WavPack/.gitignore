#
# Linux ignores
#

*.o
*.l[ao]
.deps/
Makefile
Makefile.in
aclocal.m4
ar-lib
autom4te.cache/
cli/.deps/
cli/.dirstamp
cli/.libs/
cli/wavpack
cli/wvgain
cli/wvtag
cli/wvtest
cli/wvunpack
compile
config.guess
config.log
config.rpath
config.status
config.sub
configure
depcomp
install-sh
libtool
ltmain.sh
missing
src/.deps/
src/.dirstamp
src/.libs/
wavpack.pc
wavpack*.tar.xz
*.wav
*.wv*

#
# Visual Studio ignores
#

Release/
Debug/
*.ncb
*.user
*.filters
*.suo

#
# Cygwin ignores
#

*.exe

#
# CMake ignores
#

CMakeCache.txt
CMakeFiles
CMakeScripts
Testing
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
/*[Bb]uild*/

#
# Visual Studio ignores
#

.vs/
CMakeSettings.json

#
# Visual Studio Code ignores
#

.vscode/
