////////////////////////////////////////////////////////////////////////////
//                           **** WAVPACK ****                            //
//                  Hybrid Lossless Wavefile Compressor                   //
//              Copyright (c) 1998 - 2013 Conifer Software.               //
//                          All Rights Reserved.                          //
//      Distributed under the BSD Software License (see license.txt)      //
////////////////////////////////////////////////////////////////////////////

// decorr_tables.h

// These four tables specify the characteristics of the decorrelation filters
// for the four basic compression modes (fast, normal, high, and very high).
//
// The first entry in the table represents the "default" filter for the
// corresponding mode; subsequent entries represent filters that are tried
// in the "extra" modes 1-3 ("extra" modes 4-6 create filters from scratch).
//
// The first value indicates whether the filter is applied to joint stereo
// data (0=L/R, 1=M/S) and the second value represents the "delta" value of
// the adaptive filter. The rest of the values (2-16, depending on mode) are
// the "terms" of the filter.
//
// Each term represents one layer of the sequential filter, where positive
// values indicate the relative sample involved from the same channel (1=prev),
// 17 & 18 are special functions using the previous 2 samples, and negative
// values indicate cross channel decorrelation (in stereo only).
//
// It would be ideal if this was the only source for the decorrelation tables,
// but unfortunately the defaults (first entry) are duplicated in the assembly
// code for the function pack_decorr_mono_buffer() and there is no check in
// that code to make sure the correct filter is being passed in. SO, IF A
// CHANGE IS MADE HERE TO ONE OF THE DEFAULT FILTERS, THEN THE CORRESPONDING
// ASSEMBLY CODE MUST BE CHANGED ALSO, OR VERY CORRUPT FILES WILL RESULT!!
//
// Since this include file contains that actual tables as static const data,
// it should only be included from ONE source file (currently pack.c)!

static const WavpackDecorrSpec fast_specs [] = {
        { 1, 2, {18,17} },  // 0
        { 1, 1, {17,17} },  // 1
        { 0, 2, {18,17} },  // 2
        { 0, 1, {17,17} },  // 3
        { 1, 3, { 1,18} },  // 4
        { 1, 1, {17, 1} },  // 5
        { 0, 1, { 1,17} },  // 6
        { 0, 1, {-2,17} },  // 7
        { 0, 2, {-1,17} },  // 8
        { 1, 1, {17, 2} },  // 9
        { 0, 3, {18,18} },  // 10
        { 0, 1, {17, 1} },  // 11
        { 1, 6, { 1, 2} },  // 12
        { 1, 1, {17, 3} },  // 13
        { 0, 1, {-2, 3} },  // 14
        { 0, 1, { 2,17} },  // 15
        { 0, 1, {18,-2} },  // 16
        { 0, 1, {-1,17} },  // 17
        { 0, 1, {18,17} },  // 18
        { 0, 1, {17, 2} },  // 19
        { 1, 2, {18,-2} },  // 20
        { 1, 1, { 1,17} },  // 21
        { 0, 3, {18, 2} },  // 22
        { 0, 1, {17,-2} },  // 23
        { 0, 1, {18,-2} },  // 24
        { 1, 2, {17,-3} },  // 25
        { 0, 1, {18, 3} },  // 26
        { 0, 1, {18,18} },  // 27
        { 1, 1, { 1, 3} },  // 28
        { 1, 1, {18, 3} },  // 29
        { 1, 1, { 1, 3} },  // 30
        { 0, 2, {18,17} },  // 31
        { 1, 1, { 1,17} },  // 32
        { 1, 1, {17, 3} },  // 33
        { 0, 3, {18,17} },  // 34
        { 0, 1, {18,18} },  // 35
        { 1, 1, { 1, 3} },  // 36
        { 1, 1, { 1,18} },  // 37
        { 0, 1, {18,-2} },  // 38
        { 0, 2, {18,17} },  // 39
        { 0, 1, {-1,18} },  // 40
        { 1, 1, {17, 3} },  // 41
        { 0, 1, {17, 2} },  // 42
        { 0, 1, {17, 3} },  // 43
        { 1, 1, {18, 2} },  // 44
        { 1, 1, {17,-2} },  // 45
        { 0, 1, { 1,-2} },  // 46
        { 0, 2, {18,17} },  // 47
        { 0, 1, {17,-2} },  // 48
        { 1, 1, {17,-2} },  // 49
        { 0, 1, {18, 3} },  // 50
        { 0, 1, { 2,17} },  // 51
        { 1, 2, {18,-3} },  // 52
        { 1, 2, { 1,18} },  // 53
        { 1, 2, {18, 2} },  // 54
        { 0, 1, {17,-1} },  // 55
        { 0, 1, {17,-2} },  // 56
        { 1, 1, {17,-2} },  // 57
        { 1, 1, { 1, 3} },  // 58
        { 0, 1, { 1,17} },  // 59
        { 1, 2, {18,-2} },  // 60
        { 1, 2, {17,-3} },  // 61
        { 0, 2, {18,17} },  // 62
        { 0, 2, {18,17} },  // 63
        { 1, 1, {17, 2} },  // 64
        { 1, 2, {18,18} },  // 65
        { 0, 1, {17, 2} },  // 66
        { 0, 1, {18,17} },  // 67
        { 1, 1, { 1,17} },  // 68
        { 1, 1, {17, 2} },  // 69
        { 0, 2, {18,18} },  // 70
        { 0, 2, {18,17} },  // 71
        { 1, 2, {17,-3} },  // 72
        { 1, 6, { 1, 2} },  // 73
        { 0, 3, {17,17} },  // 74
        { 0, 1, { 1,18} },  // 75
        { 0, 1, { 1,-2} },  // 76
        { 1, 1, {17, 2} },  // 77
        { 0, 2, {18,17} },  // 78
        { 0, 2, {18,17} },  // 79
        { 1, 1, {18, 3} },  // 80
        { 1, 2, {17,-3} },  // 81
        { 0, 1, {17, 2} },  // 82
        { 0, 1, {17, 3} },  // 83
        { 0, 1, {18,-2} },  // 84
        { 1, 1, {18,18} },  // 85
        { 1, 6, { 1, 2} },  // 86
        { 0, 2, {18,17} },  // 87
        { 0, 2, {18,17} },  // 88
        { 0, 1, {-1,17} },  // 89
        { 1, 1, {18, 3} },  // 90
        { 0, 1, {17,18} },  // 91
        { 1, 1, {17, 3} },  // 92
        { 0, 1, {18, 3} },  // 93
        { 0, 2, {18,17} },  // 94
        { 0, 2, {18,17} },  // 95
        { 1, 2, {18, 2} },  // 96
        { 0, 1, {-2, 3} },  // 97
        { 0, 4, {18,-1} },  // 98
        { 0, 2, {18,18} },  // 99
        { 0, 1, {-2, 3} },  // 100
        { 1, 1, {17,-2} },  // 101
        { 0, 1, {17, 3} },  // 102
        { 0, 2, {18,17} },  // 103
        { 0, 2, {-1,18} },  // 104
        { 1, 1, { 2,17} },  // 105
        { 0, 2, {17,-2} },  // 106
        { 0, 1, {17, 2} },  // 107
        { 1, 2, {18,-3} },  // 108
        { 0, 1, {17,-2} },  // 109
        { 0, 2, {18,17} },  // 110
        { 0, 2, {18,17} },  // 111
        { 1, 1, {17,-2} },  // 112
        { 1, 2, {17,-3} },  // 113
        { 1, 1, { 1, 3} },  // 114
        { 1, 1, { 2,17} },  // 115
        { 1, 2, {18, 2} },  // 116
        { 1, 1, { 2,17} },  // 117
        { 1, 1, {18, 2} },  // 118
        { 0, 2, {18,17} },  // 119
        { 0, 2, {18,17} },  // 120
        { 0, 1, {17,-2} },  // 121
        { 0, 2, {18,17} },  // 122
        { 0, 2, {17,-1} },  // 123
        { 0, 2, {18,-2} },  // 124
        { 0, 2, {18,17} },  // 125
        { 0, 2, {18,17} },  // 126
        { 0, 2, {18,17} },  // 127
        { 1, 1, { 1, 3} },  // 128
        { 0, 2, {-2,17} },  // 129
        { 0, 2, {18,-2} },  // 130
        { 0, 2, {17,-2} },  // 131
        { 1, 1, { 2,17} },  // 132
        { 1, 1, { 1, 3} },  // 133
        { 0, 1, { 2,17} },  // 134
        { 0, 2, {18,17} },  // 135
        { 0, 3, {-1,17} },  // 136
        { 1, 1, { 2,17} },  // 137
        { 0, 2, {18,18} },  // 138
        { 0, 1, {17, 2} },  // 139
        { 1, 4, {18,-3} },  // 140
        { 1, 1, {18, 1} },  // 141
        { 0, 2, {18,17} },  // 142
        { 0, 2, {18,17} },  // 143
        { 1, 2, {18,-1} },  // 144
        { 0, 1, {-1,18} },  // 145
        { 1, 6, { 1, 2} },  // 146
        { 1, 1, {17, 2} },  // 147
        { 1, 4, {18, 3} },  // 148
        { 0, 1, { 1,17} },  // 149
        { 0, 1, {18, 2} },  // 150
        { 0, 2, {18,17} },  // 151
        { 0, 2, {18,17} },  // 152
        { 1, 2, {17, 2} },  // 153
        { 0, 2, {18,-2} },  // 154
        { 0, 1, { 1,18} },  // 155
        { 1, 2, {18,-3} },  // 156
        { 0, 2, {18,17} },  // 157
        { 0, 2, {18,17} },  // 158
        { 0, 2, {18,17} },  // 159
        { 1, 2, {18,18} },  // 160
        { 1, 3, {17,17} },  // 161
        { 0, 1, {-2,17} },  // 162
        { 0, 1, {17,18} },  // 163
        { 0, 1, {-1, 3} },  // 164
        { 1, 1, { 2,17} },  // 165
        { 0, 2, {18,-1} },  // 166
        { 0, 2, {18,17} },  // 167
        { 0, 2, {18,17} },  // 168
        { 1, 1, {17,-2} },  // 169
        { 1, 2, {17, 2} },  // 170
        { 1, 1, {18, 3} },  // 171
        { 0, 1, {18, 2} },  // 172
        { 1, 2, {17,-3} },  // 173
        { 0, 2, {18,17} },  // 174
        { 0, 2, {18,17} },  // 175
        { 0, 1, {-2,17} },  // 176
        { 0, 1, {17,-1} },  // 177
        { 0, 1, {18,-1} },  // 178
        { 0, 2, {18,17} },  // 179
        { 1, 2, {17,-3} },  // 180
        { 1, 1, { 1,18} },  // 181
        { 1, 3, {18, 2} },  // 182
        { 0, 2, {18,17} },  // 183
        { 0, 2, {18,17} },  // 184
        { 0, 2, {18,17} },  // 185
        { 0, 2, {18,17} },  // 186
        { 0, 3, {18,18} },  // 187
        { 0, 1, { 1,-2} },  // 188
        { 0, 2, {18,17} },  // 189
        { 0, 2, {18,17} },  // 190
        { 0, 2, {18,17} },  // 191
        { 1, 2, {17,-3} },  // 192
        { 1, 1, {18,18} },  // 193
        { 0, 2, {18, 2} },  // 194
        { 0, 1, {17,18} },  // 195
        { 1, 2, {18, 2} },  // 196
        { 1, 1, {17,-2} },  // 197
        { 0, 2, {17,-1} },  // 198
        { 0, 2, {18,17} },  // 199
        { 0, 2, {18,17} },  // 200
        { 0, 2, {18,17} },  // 201
        { 0, 1, { 1,-2} },  // 202
        { 0, 1, {18, 1} },  // 203
        { 1, 2, {18,-2} },  // 204
        { 0, 1, {17, 2} },  // 205
        { 0, 2, {18,17} },  // 206
        { 0, 2, {18,17} },  // 207
        { 1, 1, {17, 3} },  // 208
        { 0, 1, {17,-1} },  // 209
        { 0, 1, {18, 2} },  // 210
        { 1, 1, {17, 3} },  // 211
        { 1, 1, {17,-2} },  // 212
        { 0, 1, {18,18} },  // 213
        { 0, 2, {18,17} },  // 214
        { 0, 2, {18,17} },  // 215
        { 0, 2, {18,17} },  // 216
        { 0, 2, {18,17} },  // 217
        { 0, 2, {18,17} },  // 218
        { 1, 1, {17,18} },  // 219
        { 0, 1, {-2, 3} },  // 220
        { 0, 2, {18,17} },  // 221
        { 0, 2, {18,17} },  // 222
        { 0, 2, {18,17} },  // 223
        { 1, 2, {18,-3} },  // 224
        { 0, 2, {18,17} },  // 225
        { 0, 3, {18, 2} },  // 226
        { 0, 1, { 1,18} },  // 227
        { 0, 2, {18,17} },  // 228
        { 0, 1, {17,-1} },  // 229
        { 0, 2, {18,17} },  // 230
        { 0, 2, {18,17} },  // 231
        { 0, 2, {18,17} },  // 232
        { 0, 1, {-2, 3} },  // 233
        { 0, 3, {17,17} },  // 234
        { 0, 2, {18,17} },  // 235
        { 0, 2, {18,17} },  // 236
        { 1, 1, {17, 2} },  // 237
        { 0, 2, {18,17} },  // 238
        { 0, 2, {18,17} },  // 239
        { 1, 1, {17, 2} },  // 240
        { 0, 2, {18,17} },  // 241
        { 0, 2, {18,17} },  // 242
        { 0, 2, {18,17} },  // 243
        { 0, 2, {18, 2} },  // 244
        { 0, 2, {18,17} },  // 245
        { 0, 2, {18,17} },  // 246
        { 0, 2, {18,17} },  // 247
        { 0, 2, {18,17} },  // 248
        { 0, 2, {18,17} },  // 249
        { 0, 2, {18,17} },  // 250
        { 0, 2, {18,17} },  // 251
        { 0, 2, {18,17} },  // 252
        { 0, 2, {18,17} },  // 253
        { 0, 2, {18,17} },  // 254
        { 0, 2, {18,17} },  // 255
};

static const WavpackDecorrSpec default_specs [] = {
        { 1, 2, {18,18, 2,17, 3} },         // 0
        { 0, 2, {18,17,-1, 3, 2} },         // 1
        { 1, 1, {17,18,18,-2, 2} },         // 2
        { 0, 2, {18,17, 3,-2,17} },         // 3
        { 1, 2, {18,17, 2,17, 3} },         // 4
        { 0, 1, {18,18,-1, 2,17} },         // 5
        { 0, 1, {17,17,-2, 2, 3} },         // 6
        { 0, 1, {18,-2,18, 2,17} },         // 7
        { 1, 2, {18,18,-1, 2, 3} },         // 8
        { 0, 2, {18,17, 3, 2, 5} },         // 9
        { 1, 1, {18,17,18, 2, 5} },         // 10
        { 0, 1, {17,17,-2, 2, 3} },         // 11
        { 0, 1, {18,-2,18, 2, 5} },         // 12
        { 0, 1, {17,-2,17, 2,-3} },         // 13
        { 1, 1, {17,-2,17, 1, 2} },         // 14
        { 0, 1, {17,17,-2, 2, 3} },         // 15
        { 1, 1, {18, 3, 1, 5, 4} },         // 16
        { 1, 4, {18,18, 2, 3,-2} },         // 17
        { 0, 1, { 1,-1,-1, 2,17} },         // 18
        { 0, 2, {18,17, 3, 2, 5} },         // 19
        { 0, 1, {18,18,18, 2,17} },         // 20
        { 0, 1, {18,17,-1, 2,18} },         // 21
        { 1, 1, {17, 3, 2, 1, 7} },         // 22
        { 0, 2, {18,-2,18, 2, 3} },         // 23
        { 1, 3, {18,-3,18, 2, 3} },         // 24
        { 0, 3, {18,17, 2, 3,17} },         // 25
        { 1, 1, {17,17, 2, 1, 4} },         // 26
        { 0, 1, {17,18,-2, 2,17} },         // 27
        { 1, 1, {18,18, 3, 5, 2} },         // 28
        { 0, 1, {17,17, 2,18, 4} },         // 29
        { 0, 1, {18,17, 1, 4, 6} },         // 30
        { 1, 1, { 3,17,18, 2,17} },         // 31
        { 1, 1, {17, 3, 2, 1, 7} },         // 32
        { 0, 1, {18,17,-1, 2, 3} },         // 33
        { 1, 1, {17,17, 2, 1, 4} },         // 34
        { 1, 2, {18,17,-1,17, 3} },         // 35
        { 1, 2, {18,17, 2, 3,-1} },         // 36
        { 0, 2, {18,18,-2, 2,17} },         // 37
        { 0, 1, {17,17, 2,18, 4} },         // 38
        { 0, 5, {-2,18,18,18, 2} },         // 39
        { 1, 1, {18,18,-1, 6, 3} },         // 40
        { 0, 1, {17,17,-2, 2, 3} },         // 41
        { 1, 1, {18,17,18, 2,17} },         // 42
        { 0, 1, {18,17, 4, 3, 1} },         // 43
        { 0, 1, {-2,18, 2, 2,18} },         // 44
        { 1, 2, {18,18,-2, 2,-1} },         // 45
        { 1, 1, {17,17, 2, 1, 4} },         // 46
        { 0, 1, {17,18,-2, 2,17} },         // 47
        { 1, 1, {17, 3, 2, 1, 7} },         // 48
        { 1, 3, {18,-3,18, 2, 3} },         // 49
        { 1, 2, {18,18,-2, 2,-1} },         // 50
        { 1, 1, {18,18, 3, 5, 2} },         // 51
        { 0, 2, {18,18,-1, 2,17} },         // 52
        { 0, 1, {18,-1,17,18, 2} },         // 53
        { 0, 1, {17,-1, 2, 3, 6} },         // 54
        { 0, 1, {18,-2,18, 2, 5} },         // 55
        { 1, 2, {18,18,-2, 2,-1} },         // 56
        { 0, 3, {18,18, 2, 3,17} },         // 57
        { 0, 1, {17,17, 2,18, 4} },         // 58
        { 1, 1, {17,-2,17, 1, 2} },         // 59
        { 0, 1, {-1, 3, 5, 4, 7} },         // 60
        { 0, 3, {18,18, 3, 2, 5} },         // 61
        { 0, 1, {17,17, 2,18, 4} },         // 62
        { 0, 1, {18,17,-2,18, 3} },         // 63
        { 0, 2, {18,18,-2, 2,17} },         // 64
        { 0, 3, {18,17,-2, 2, 3} },         // 65
        { 1, 1, {18,18,-2, 2,17} },         // 66
        { 0, 1, {18,17, 4, 3, 1} },         // 67
        { 1, 2, { 3,18,17, 2,17} },         // 68
        { 1, 2, {18,18, 2,-2,18} },         // 69
        { 1, 2, {18,18,-1,18, 2} },         // 70
        { 0, 2, {18,18,-2, 2,17} },         // 71
        { 1, 3, {18,18, 2, 3,-2} },         // 72
        { 0, 3, {18,18, 3, 2, 5} },         // 73
        { 0, 1, {18,-2,18, 2, 5} },         // 74
        { 1, 1, {17, 3, 2, 1, 7} },         // 75
        { 1, 3, {18,18,-2, 2,18} },         // 76
        { 1, 1, {17,18,18,-2, 2} },         // 77
        { 0, 1, {18,-2,18, 2, 5} },         // 78
        { 0, 2, {18,-2,18, 2, 3} },         // 79
        { 0, 1, {-1, 3, 4, 5, 7} },         // 80
        { 1, 1, {17,17, 2,-1, 7} },         // 81
        { 0, 1, {18,-1,-1, 2,-2} },         // 82
        { 0, 2, {18,17, 2, 3,17} },         // 83
        { 0, 1, {18,17, 2,18, 2} },         // 84
        { 0, 2, {18,17,-1, 2,17} },         // 85
        { 0, 1, { 1,18, 3, 2, 5} },         // 86
        { 0, 2, {18,-2, 4,18, 2} },         // 87
        { 1, 1, {18, 3, 1, 5, 4} },         // 88
        { 0, 1, {18,17,18, 2, 5} },         // 89
        { 1, 1, {18, 3, 1, 5, 4} },         // 90
        { 0, 4, {18,18,-2, 2,18} },         // 91
        { 1, 1, {18,18, 3, 2, 5} },         // 92
        { 1, 1, {17,17, 2, 1, 4} },         // 93
        { 0, 2, {18,18,-2,18, 2} },         // 94
        { 0, 2, {18,18,-2,18, 2} },         // 95
        { 1, 1, {18,18, 2, 1, 3} },         // 96
        { 1, 1, {17,17, 2, 1, 4} },         // 97
        { 1, 2, {17,17, 2,18, 3} },         // 98
        { 0, 1, {18,17, 1, 4, 6} },         // 99
        { 1, 2, {18,18,-2, 2,-1} },         // 100
        { 0, 1, {18,-2,18, 2, 5} },         // 101
        { 1, 1, {17, 2,18, 2,17} },         // 102
        { 0, 2, {18,18,-2,18, 2} },         // 103
        { 0, 1, {18,18, 3, 6,-1} },         // 104
        { 0, 1, {18,17, 2,18, 3} },         // 105
        { 0, 1, {18,17,-2, 2,17} },         // 106
        { 1, 1, { 3,17,18, 2,17} },         // 107
        { 1, 3, {18,-3,18, 2, 3} },         // 108
        { 1, 3, {18,18,-3,18, 2} },         // 109
        { 1, 1, {18, 3, 1, 5, 4} },         // 110
        { 0, 1, {17,-2,17, 2,-3} },         // 111
        { 1, 1, {18,18, 3, 5, 2} },         // 112
        { 1, 2, {18,18,-2, 2,-1} },         // 113
        { 0, 1, {18,-1,-1, 2,-2} },         // 114
        { 1, 1, {18, 3, 1, 5, 4} },         // 115
        { 0, 3, {18,17,-1, 2,17} },         // 116
        { 1, 3, {18,17, 2,18,-2} },         // 117
        { 0, 2, {18,18,-2,18, 2} },         // 118
        { 1, 2, {18,18,-2, 2,-1} },         // 119
        { 1, 1, {18, 3, 1, 5, 4} },         // 120
        { 0, 4, { 3,18,18, 2,17} },         // 121
        { 0, 2, {18,18,-2,18, 2} },         // 122
        { 1, 1, {18,17,-1,18, 2} },         // 123
        { 0, 2, {18,18,-2,18, 2} },         // 124
        { 0, 2, {18,18,-2,18, 2} },         // 125
        { 0, 2, {18,18,-2,18, 2} },         // 126
        { 0, 2, {18,18,-2,18, 2} },         // 127
        { 1, 1, {18,18,18, 3, 2} },         // 128
        { 0, 1, {17,-1, 2, 3, 6} },         // 129
        { 0, 1, {17,-1, 2, 3, 6} },         // 130
        { 0, 2, {18,17,-2, 3, 2} },         // 131
        { 1, 3, {18,17, 2,-2,18} },         // 132
        { 0, 2, {18,18, 2,17, 3} },         // 133
        { 0, 1, {18,18, 2,18,-2} },         // 134
        { 0, 2, {18,-2, 4,18, 2} },         // 135
        { 0, 1, {-2,18, 2, 2,18} },         // 136
        { 0, 2, {18,17, 3, 6, 2} },         // 137
        { 0, 1, {18,17,18, 2, 5} },         // 138
        { 0, 3, {18,18,-2, 3, 2} },         // 139
        { 1, 1, {18,18, 2,18, 5} },         // 140
        { 0, 1, {17,-1, 2, 3, 6} },         // 141
        { 1, 4, {18,18, 2, 3,-2} },         // 142
        { 0, 2, {18,17,18, 2,-2} },         // 143
        { 0, 1, { 1,18, 3, 2, 5} },         // 144
        { 1, 4, {18,-2,18, 2, 3} },         // 145
        { 1, 2, {18, 2,18, 3,-2} },         // 146
        { 0, 2, {18,18,18, 2, 4} },         // 147
        { 0, 2, { 3,17,18, 2,17} },         // 148
        { 1, 1, {18,-1,18, 2,17} },         // 149
        { 1, 2, {17,17, 2,18, 3} },         // 150
        { 0, 2, {18,17,-2, 3, 2} },         // 151
        { 0, 1, { 1,-1,-1, 2,17} },         // 152
        { 0, 3, { 3,18,18, 2,17} },         // 153
        { 0, 1, {18,-1,17,18, 2} },         // 154
        { 0, 1, {18,17, 2,18, 3} },         // 155
        { 0, 2, {18,18,-2,18, 2} },         // 156
        { 0, 1, {18,17, 2,18, 2} },         // 157
        { 0, 2, {18,18,-2,18, 2} },         // 158
        { 0, 2, {18,18,-2,18, 2} },         // 159
        { 1, 2, {17,17, 2,18, 3} },         // 160
        { 0, 1, {18,17,-2, 2, 3} },         // 161
        { 0, 1, {18,-2,18, 2, 5} },         // 162
        { 1, 4, {18,-2,18, 2, 3} },         // 163
        { 1, 3, {18,17, 2, 3, 6} },         // 164
        { 0, 2, {18,18, 2,17, 3} },         // 165
        { 0, 2, {18,17, 2,18, 2} },         // 166
        { 0, 2, {18,18,-2,18, 2} },         // 167
        { 1, 1, {18,18, 3, 5, 2} },         // 168
        { 0, 2, {18,18,-2, 2, 3} },         // 169
        { 1, 2, {18,17, 2,17, 3} },         // 170
        { 0, 1, {18,17, 2, 3,18} },         // 171
        { 0, 2, {18,18,-2,18, 2} },         // 172
        { 1, 4, {18,18, 2, 3,-2} },         // 173
        { 0, 1, {17,-2,17, 2,-3} },         // 174
        { 0, 1, {17,17, 2,18, 4} },         // 175
        { 1, 1, {18,18,18, 2, 4} },         // 176
        { 1, 2, {18, 2,18, 3,-2} },         // 177
        { 1, 1, {18,18,-2, 2,17} },         // 178
        { 0, 2, {18,18,-2,18, 2} },         // 179
        { 0, 2, {18,18, 2,17, 3} },         // 180
        { 0, 2, {18,18,18, 2, 4} },         // 181
        { 0, 2, {18,18,-2,18, 2} },         // 182
        { 0, 2, {18,17,-2, 3, 2} },         // 183
        { 0, 1, { 1,-1,-1, 2,17} },         // 184
        { 1, 4, {18,18, 2, 3,-2} },         // 185
        { 0, 2, {18,18,-2,18, 2} },         // 186
        { 0, 1, {18,-2,18, 3, 2} },         // 187
        { 0, 2, {18,18,-2,18, 2} },         // 188
        { 0, 2, {18,18,-2,18, 2} },         // 189
        { 0, 2, {18,18,-2,18, 2} },         // 190
        { 0, 2, {18,18,-2,18, 2} },         // 191
        { 0, 1, {18,18,-2, 2,17} },         // 192
        { 0, 3, {18,17, 2, 3,17} },         // 193
        { 1, 2, {18,18, 2,-2,18} },         // 194
        { 0, 1, {-1, 3, 5, 4, 7} },         // 195
        { 1, 1, {18, 3, 1, 5, 4} },         // 196
        { 1, 1, {18,18,-2,18, 3} },         // 197
        { 0, 2, {18,17,18, 2,-2} },         // 198
        { 0, 2, {18,18, 2,17, 3} },         // 199
        { 1, 2, {18, 2,18, 3,-2} },         // 200
        { 1, 4, {18,18, 2, 3,-2} },         // 201
        { 1, 3, {18,17, 2, 3, 6} },         // 202
        { 0, 2, {18,18,-2,18, 2} },         // 203
        { 1, 2, {18,17,-2,-1,17} },         // 204
        { 0, 1, {17,-1, 2, 3, 6} },         // 205
        { 0, 2, {18,18,-2,18, 2} },         // 206
        { 0, 2, {18,18,-2, 2, 3} },         // 207
        { 1, 1, {18,18,18, 2, 5} },         // 208
        { 0, 1, {17,17,-2, 2, 3} },         // 209
        { 0, 2, {18,18,-2,18, 2} },         // 210
        { 0, 2, {18,17, 3, 6, 2} },         // 211
        { 0, 2, {18,17,18, 2, 3} },         // 212
        { 0, 3, {18,17,-3,18, 2} },         // 213
        { 0, 1, {18,18,18, 2, 3} },         // 214
        { 0, 1, {18,-2,-3, 2, 6} },         // 215
        { 0, 2, {18,18,-2,18, 2} },         // 216
        { 1, 1, {18,17,18, 2, 5} },         // 217
        { 0, 2, {18,18,-2,18, 2} },         // 218
        { 0, 2, {18,18,-2,18, 2} },         // 219
        { 1, 1, {18,17,18, 2, 5} },         // 220
        { 0, 2, {18,18,-2,18, 2} },         // 221
        { 0, 2, {18,18,-2,18, 2} },         // 222
        { 0, 2, {18,18,-2,18, 2} },         // 223
        { 0, 1, {18,18,18, 2, 3} },         // 224
        { 1, 1, {17,-2,17, 1, 2} },         // 225
        { 1, 1, {17,17, 2,-1, 7} },         // 226
        { 0, 1, {18,17, 4, 3, 1} },         // 227
        { 1, 3, {18,-3,18, 2, 3} },         // 228
        { 0, 1, { 1,18, 3, 2, 5} },         // 229
        { 0, 2, {18,18,-2,18, 2} },         // 230
        { 0, 2, {18,18,-2,18, 2} },         // 231
        { 0, 1, {18,18, 3, 6, 2} },         // 232
        { 0, 1, {17,17, 2,18, 4} },         // 233
        { 0, 1, {17,17, 2,18, 4} },         // 234
        { 0, 2, {18,18,-2,18, 2} },         // 235
        { 0, 2, {18,18,-2,18, 2} },         // 236
        { 0, 2, {18,18,-2,18, 2} },         // 237
        { 1, 2, {18,-2,18, 3, 2} },         // 238
        { 1, 1, {17,-2,17, 1, 2} },         // 239
        { 1, 1, {18,18, 3, 2, 5} },         // 240
        { 0, 1, {18,18,-1, 2, 3} },         // 241
        { 0, 2, {18,18,-2,18, 2} },         // 242
        { 0, 2, {18,18,-2,18, 2} },         // 243
        { 0, 1, {18,17,18, 2, 5} },         // 244
        { 0, 2, {18,18,-2,18, 2} },         // 245
        { 0, 2, {18,18,-2,18, 2} },         // 246
        { 0, 2, {18,18,-2,18, 2} },         // 247
        { 0, 2, {18,18,-2,18, 2} },         // 248
        { 0, 1, { 3,18,18, 2,17} },         // 249
        { 0, 2, {18,18,-2,18, 2} },         // 250
        { 0, 2, {18,18,-2,18, 2} },         // 251
        { 0, 2, {18,18,-2,18, 2} },         // 252
        { 0, 2, {18,18,-2,18, 2} },         // 253
        { 0, 2, {18,18,-2,18, 2} },         // 254
        { 0, 2, {18,18,-2,18, 2} },         // 255
};

static const WavpackDecorrSpec high_specs [] = {
        { 1, 2, {18,18,18,-2, 2, 3, 5,-1,17, 4} },  // 0
        { 0, 1, {18,17,-2, 2,18, 3, 7, 2, 5, 4} },  // 1
        { 1, 2, { 1,18, 3, 6,-2,18, 2, 3, 4, 5} },  // 2
        { 0, 2, {18,18,-2, 2,18, 3, 6, 2,17, 4} },  // 3
        { 1, 2, {18,18, 2,18, 3, 2,-1, 4,18, 5} },  // 4
        { 1, 1, { 7, 6, 5, 3, 4, 2, 5, 4, 3, 7} },  // 5
        { 1, 1, {17, 3,18, 7, 2, 6, 1, 4, 3, 5} },  // 6
        { 1, 1, {-2,18,18,18, 3,-2, 6, 5, 2, 1} },  // 7
        { 1, 2, {18,18,-1,18, 2, 3, 6,-2,17, 5} },  // 8
        { 0, 1, {17,17,18, 3, 6, 4, 5, 2,18,-2} },  // 9
        { 1, 2, { 1,18,-2, 3, 5, 2, 4,-1, 6, 1} },  // 10
        { 0, 2, {18,18, 3, 6,18, 2, 4, 8, 5, 3} },  // 11
        { 0, 1, {-2, 1,18, 2,-2, 7,18, 2,-1, 5} },  // 12
        { 1, 1, { 4, 3, 8, 1, 5, 2, 5, 6, 2, 8} },  // 13
        { 1, 1, {17,18, 2, 6, 3, 4,-1, 1, 8, 6} },  // 14
        { 0, 1, {18,18, 3, 6, 3,-2, 2, 5,-1, 1} },  // 15
        { 0, 1, {18,18,17,-1, 2,-2,18, 3, 4, 5} },  // 16
        { 1, 2, {18,17, 2,-2,18, 3, 5, 7, 2, 4} },  // 17
        { 1, 2, {18,18, 3, 6,-2,18, 2, 5, 8, 3} },  // 18
        { 0, 1, {18,17, 2,18,18, 2, 6, 5,17, 7} },  // 19
        { 1, 2, {18,17, 2,18, 3, 2, 6,18,-1, 4} },  // 20
        { 1, 1, { 5, 3, 6, 5, 3, 4, 1, 2, 4, 7} },  // 21
        { 1, 1, { 5, 3, 6, 5, 3, 4, 1, 2, 4, 7} },  // 22
        { 0, 1, {-2,18,18,18,-2, 3, 2, 4, 6, 5} },  // 23
        { 1, 2, {18,17,-3, 3,-1,18, 2, 3, 6, 5} },  // 24
        { 0, 1, {17,18, 7, 3,-2, 7, 1, 2, 4, 5} },  // 25
        { 1, 1, { 2,18,18,-2, 2, 4,-1,18, 3, 6} },  // 26
        { 0, 3, { 1,18, 4, 3, 5, 2, 4,18, 2, 3} },  // 27
        { 0, 1, {-2,18, 2,18, 3, 7,18, 2, 6,-2} },  // 28
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 29
        { 1, 1, {18,18, 5, 4, 6, 4, 5, 1, 4, 3} },  // 30
        { 1, 1, {18, 3, 6, 5, 7, 8, 2, 3, 1,-1} },  // 31
        { 1, 1, {18,18,18, 2,-2, 3, 5,18, 2, 8} },  // 32
        { 0, 2, {18,17,-2, 2, 3,18,-3, 5, 2, 7} },  // 33
        { 1, 1, { 1, 1,-1, 8,17, 3,-2, 2, 6,17} },  // 34
        { 0, 2, {18,18,17, 2,-2, 3, 2, 4,18, 5} },  // 35
        { 1, 1, {17,18, 2,-1, 5, 7,18, 3, 4, 6} },  // 36
        { 1, 1, { 5, 4, 5,17, 3, 6, 3, 4, 7, 2} },  // 37
        { 0, 1, {17, 3, 1, 7, 4, 2, 5,-2,18, 6} },  // 38
        { 0, 1, {17,18, 2,18, 4, 3, 5, 7,-3, 6} },  // 39
        { 1, 2, {17,17,-3,-2, 2, 8,18,-1, 3, 5} },  // 40
        { 0, 1, {17,17,18, 2, 3, 6,-2, 8, 1, 7} },  // 41
        { 1, 1, { 1, 2, 6,-2,18, 2, 5,-3, 7,-2} },  // 42
        { 0, 1, {18,18, 3,18, 6, 8,-2, 2, 3, 5} },  // 43
        { 0, 1, {18,17, 2,18,-2, 3, 7, 6, 2, 4} },  // 44
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 45
        { 1, 1, {18,18, 2,-1, 3, 6, 1, 3, 4, 8} },  // 46
        { 0, 1, {18,18, 3, 6, 5, 3,-2, 2,18,-1} },  // 47
        { 0, 1, {18,17,-3,18, 2, 4,-2, 3, 6,17} },  // 48
        { 1, 3, { 1, 2,17, 3,18, 7,-1, 5, 2, 4} },  // 49
        { 1, 1, {18, 3,18, 6, 8,18,-2, 5, 7, 2} },  // 50
        { 0, 1, {17, 2,18, 6, 3, 2, 5, 4, 8, 1} },  // 51
        { 0, 1, {18,17,-1, 2, 3,18,18, 2, 3,17} },  // 52
        { 1, 1, {18, 7, 6, 5, 5, 3, 1, 4, 2, 4} },  // 53
        { 1, 1, { 6,17, 3, 8, 1, 5, 7,-1, 2, 1} },  // 54
        { 1, 1, {18,-2,18, 3,-2, 2, 7, 4, 6,18} },  // 55
        { 1, 3, {18,-3,18, 2, 3,18,-1, 7, 2, 5} },  // 56
        { 0, 2, {18,-2, 7, 1, 3, 2, 4, 6,-3, 7} },  // 57
        { 1, 1, {18,-2, 2,-3,18,-2,17,-1, 4, 2} },  // 58
        { 0, 3, {17,17, 2, 5, 3, 7,18, 6, 4, 2} },  // 59
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 60
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 61
        { 1, 1, {18,17, 4, 6, 6, 4, 5, 3, 4, 1} },  // 62
        { 0, 1, {18, 5, 3, 6, 2, 3, 8, 1, 3, 7} },  // 63
        { 1, 2, {18,17,-2, 2,18, 3, 5, 7,-1, 2} },  // 64
        { 0, 1, { 1,18,18, 3, 6,-1, 4, 8, 5, 2} },  // 65
        { 1, 1, { 1, 5, 3, 4, 1, 1, 3, 5, 7, 3} },  // 66
        { 0, 1, { 3,18,18, 2,18,18,-1, 2, 3,18} },  // 67
        { 1, 2, {18,18,-1,18, 2, 3, 4, 6,18, 5} },  // 68
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 69
        { 1, 1, {18, 3, 1, 4, 5, 2, 7, 1, 3, 6} },  // 70
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 71
        { 1, 2, {18,18,-1,18, 2, 3, 5,-2, 6, 8} },  // 72
        { 1, 1, {17,18, 4, 8, 3, 2, 5, 2, 7, 6} },  // 73
        { 1, 4, { 1, 2, 5,18,-2, 2, 3, 7,-1, 4} },  // 74
        { 0, 2, {18,17,-1, 3, 6,18, 2, 3, 7, 5} },  // 75
        { 0, 1, {-2,18, 2,-3, 6,18, 4, 3,-2, 5} },  // 76
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 77
        { 0, 1, {17,17, 6, 2, 4, 8, 3, 5,-1,17} },  // 78
        { 1, 1, {18, 3,18, 6, 8,18,-2, 5, 7, 2} },  // 79
        { 1, 2, {17,17,-3, 2,18,-2, 8, 3, 6,-1} },  // 80
        { 1, 1, {18,-2,17,18, 2, 3,-2, 6, 5, 4} },  // 81
        { 1, 2, {18,17,-1, 3,18, 2, 5, 3, 6,-3} },  // 82
        { 0, 1, {18,17, 2,18, 7,18, 2, 4, 3,17} },  // 83
        { 1, 3, {18,18, 5, 6, 4, 3, 4,18, 6, 5} },  // 84
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 85
        { 1, 1, { 7, 6, 5, 3, 4, 2, 5, 4, 3, 7} },  // 86
        { 0, 1, {-2,18,18,18, 3, 6, 4, 2, 5, 2} },  // 87
        { 0, 3, {18,17,-3,18, 3, 2, 5,-1,17, 3} },  // 88
        { 1, 1, {17,18, 7, 3, 1, 7, 4, 2, 6, 5} },  // 89
        { 1, 1, {18, 2,-2,-1,18, 5, 3,-2, 1, 2} },  // 90
        { 0, 3, {18,18,-1, 3, 2, 7, 5,18, 4, 3} },  // 91
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 92
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 93
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 94
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 95
        { 1, 1, {17,18, 2,-2, 4, 8,18, 3, 6, 5} },  // 96
        { 0, 2, {18,17, 3, 5,-2, 7, 2,18, 3,-1} },  // 97
        { 1, 1, {18, 2,-2,-1,18, 5, 3,-2, 1, 2} },  // 98
        { 0, 2, { 3,17,18,18, 2, 5, 7, 6,18, 3} },  // 99
        { 1, 1, {17,18,18, 4, 3, 2,18, 7, 8,-1} },  // 100
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 101
        { 0, 1, {17, 1, 2, 3, 5, 6, 1, 4, 8,17} },  // 102
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 103
        { 0, 2, {18,17,-1,18,-3, 2, 8, 3, 6,17} },  // 104
        { 1, 1, {17,17, 1, 2, 4, 5,-1, 2, 1, 6} },  // 105
        { 1, 1, { 1, 2, 6,-2,18, 2,-3, 3,-2, 5} },  // 106
        { 0, 1, {18, 3,18, 6,18, 5, 2, 4,-1, 8} },  // 107
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 108
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 109
        { 1, 1, {18,18,-1, 2,18, 3, 6, 4,-2, 7} },  // 110
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 111
        { 0, 2, {-1,18,18,18, 2,-2, 4, 7, 2, 3} },  // 112
        { 0, 3, { 3,17,-2, 5, 2, 7,18, 6, 4, 5} },  // 113
        { 0, 1, {17, 6,18, 3, 8, 4, 5, 3, 8,18} },  // 114
        { 0, 2, {18, 2, 6, 2,18, 3, 2, 4, 5, 8} },  // 115
        { 0, 1, { 3,18,18, 2,18,-1, 2,18, 2,17} },  // 116
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 117
        { 0, 1, { 3, 6,17,-2, 5, 1, 2, 7, 4, 8} },  // 118
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 119
        { 1, 3, { 3,18,17, 5, 6, 2, 7,-2, 8,18} },  // 120
        { 1, 1, {18,-1, 3, 1, 7, 2,-1, 4, 6,17} },  // 121
        { 1, 1, {18, 2,-2,-1,18, 5, 3,-2, 1, 2} },  // 122
        { 0, 2, {18, 1, 2,18, 3, 6, 5, 2, 4, 8} },  // 123
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 124
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 125
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 126
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 127
        { 1, 1, {17,-2, 2,18,18, 8, 5, 3, 2, 6} },  // 128
        { 0, 1, {18,17, 2,18, 3, 2, 7,-2,18, 4} },  // 129
        { 1, 2, { 1,18, 2, 3,-1, 5, 6, 4, 7,17} },  // 130
        { 0, 2, {18,17, 3, 6,-2, 2, 3, 8, 5,17} },  // 131
        { 0, 2, {18,18, 3, 2,18,-1, 2, 4, 3,17} },  // 132
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 133
        { 1, 2, {17,-1,18, 2, 3,-2, 5,18, 2, 7} },  // 134
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 135
        { 1, 2, {18,-3,18, 2, 3,-2,18, 5, 6,-3} },  // 136
        { 0, 2, {18,17, 3, 5,-2, 7, 2,18, 3,-1} },  // 137
        { 1, 1, { 1,18,-1, 2, 3, 1,-2, 8, 2, 5} },  // 138
        { 0, 1, {18,18, 3, 6,18, 2, 3, 4, 8, 5} },  // 139
        { 0, 1, {-2, 1,18, 2,-2, 5, 7,18, 2,-1} },  // 140
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 141
        { 1, 1, {17,18,-1, 2, 8, 3, 4, 5, 1, 7} },  // 142
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 143
        { 0, 2, {18,18,-1, 2,18, 3,-2, 5, 4, 2} },  // 144
        { 1, 1, {18,17, 2,18, 3, 8, 5, 2, 7,17} },  // 145
        { 0, 1, {18,18, 3,18, 6, 8,-2, 2, 3, 5} },  // 146
        { 0, 1, {18,18, 2,18, 2, 6,18, 2,17, 7} },  // 147
        { 1, 3, {18,17,18, 2, 8,18, 5,-1, 3, 6} },  // 148
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 149
        { 1, 1, {18, 7, 6, 5, 5, 3, 1, 4, 2, 4} },  // 150
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 151
        { 1, 2, {18,17,-1, 3, 6,18, 2, 5, 8, 3} },  // 152
        { 0, 1, {17,18,18, 4, 7, 2, 3,-2,18, 5} },  // 153
        { 1, 2, {18, 1, 2, 6, 2, 5,18, 2, 4, 8} },  // 154
        { 0, 4, {18, 4, 1, 2, 3, 5, 4, 1, 2, 6} },  // 155
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 156
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 157
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 158
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 159
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 160
        { 0, 2, {18,17, 2,-1,18, 3,-3, 5, 2, 4} },  // 161
        { 0, 1, {17,17, 3, 6, 3, 5,-2, 2,18,-1} },  // 162
        { 0, 2, {18,18, 3,-2,18, 2,-3, 5, 3, 6} },  // 163
        { 1, 1, {17,17, 2, 4, 1, 3, 5, 2, 6,-3} },  // 164
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 165
        { 0, 1, {17, 1, 3, 2, 7, 1, 6, 3, 4, 8} },  // 166
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 167
        { 0, 1, {17,-1,18, 2, 1, 5, 3, 8,-1,-2} },  // 168
        { 1, 1, {17,18,-1, 8, 2, 5, 3, 4, 1, 6} },  // 169
        { 1, 2, { 1,18, 3,-1, 5, 1, 2, 4, 7, 6} },  // 170
        { 0, 1, {18,18, 3, 6, 5, 3,-2, 2,18,-1} },  // 171
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 172
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 173
        { 0, 1, { 1,18,-1, 3, 8, 5, 6, 1, 2, 3} },  // 174
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 175
        { 0, 2, {18,18, 2, 3, 6,18,-1, 4, 2, 3} },  // 176
        { 1, 1, { 1, 3, 5,18, 2, 6, 7, 2, 3, 1} },  // 177
        { 1, 1, { 1, 3, 8,18, 5, 2, 7, 1, 3,-2} },  // 178
        { 0, 2, {17, 2,18, 3, 6, 2, 4, 5, 8, 3} },  // 179
        { 0, 1, {18,17, 2,18, 3, 2, 7,-2,18, 4} },  // 180
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 181
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 182
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 183
        { 1, 2, {18,-3,18,-1, 3,-2, 5, 7, 1, 2} },  // 184
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 185
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 186
        { 0, 3, {18,18, 2, 6,18, 5,18, 2, 3,17} },  // 187
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 188
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 189
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 190
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 191
        { 1, 3, { 1,-1, 1, 3,-2, 2, 5, 7,-3,18} },  // 192
        { 1, 2, {18, 7, 3,-3, 2, 8, 2, 5, 4,17} },  // 193
        { 1, 1, { 1, 4, 5, 1, 3, 4, 6, 7, 8, 3} },  // 194
        { 0, 1, {18,17, 2,18,-1, 2, 3,18, 2, 4} },  // 195
        { 0, 2, {18,18,-2,18, 2, 3, 4, 7, 5,17} },  // 196
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 197
        { 1, 1, {17,18, 2, 1, 3, 2, 5, 1, 2, 3} },  // 198
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 199
        { 0, 2, {18,18,-1, 2, 3, 5, 8, 6, 1,-2} },  // 200
        { 0, 1, {17,18, 8, 3, 4, 6, 5, 2, 8, 7} },  // 201
        { 1, 2, { 1, 3,-2,18, 2, 5, 1, 7,-1,-2} },  // 202
        { 0, 3, {18,17,-1, 3,18, 2, 3, 6, 4,17} },  // 203
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 204
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 205
        { 1, 2, {18,18, 4,18, 6, 7, 8, 3,18, 2} },  // 206
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 207
        { 0, 2, {17,-3,17, 2,-2, 8, 3,18, 4,-3} },  // 208
        { 1, 1, {18,17, 3, 5, 6, 2, 8, 1, 3, 7} },  // 209
        { 0, 1, {18,18, 3, 6, 5, 3,-2, 2,18,-1} },  // 210
        { 0, 3, {18,18, 2, 6,18, 5,18, 2, 3,17} },  // 211
        { 1, 1, {18,18, 5, 4, 6, 4, 5, 1, 4, 3} },  // 212
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 213
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 214
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 215
        { 0, 2, { 3,17,18,-3, 2, 5,18, 6,-1, 7} },  // 216
        { 1, 1, {17,18, 3, 2, 5,-1, 6, 8, 4, 7} },  // 217
        { 1, 1, {18, 1,-2, 3, 2, 1, 7, 6, 3, 4} },  // 218
        { 0, 3, { 1, 2,17, 3,18, 2, 7, 5, 4,-1} },  // 219
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 220
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 221
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 222
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 223
        { 1, 1, {17,-2, 2,18,18, 8, 5, 3, 2, 6} },  // 224
        { 0, 2, {18, 5,18, 2, 3, 7,-2, 1, 6, 8} },  // 225
        { 0, 1, { 2,-1,18,-1, 2, 4,-3, 5,18, 3} },  // 226
        { 0, 1, { 3,17,18, 5, 2,18, 7, 3, 6, 5} },  // 227
        { 1, 4, { 1, 2, 5,18,-2, 2, 3, 7,-1, 4} },  // 228
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 229
        { 0, 1, { 1,18, 2, 1, 3, 4, 1, 5, 2, 7} },  // 230
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 231
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 232
        { 0, 1, {17,17,18, 2, 4, 5,18,-2, 6, 3} },  // 233
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 234
        { 0, 2, {18,18,-1, 3, 5, 6, 8,18, 2, 3} },  // 235
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 236
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 237
        { 0, 1, {18,18, 4, 6, 8,18, 7, 3, 2, 5} },  // 238
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 239
        { 0, 2, {-1,18,18,18, 2, 4,-2, 2, 3, 6} },  // 240
        { 0, 2, {18,-2, 7, 1, 3, 2, 4, 6,-3, 7} },  // 241
        { 1, 1, {17,18, 8, 3, 4, 6,-2, 5, 3, 8} },  // 242
        { 0, 2, {18, 1, 2, 6, 2, 8, 3,18, 5, 4} },  // 243
        { 1, 1, { 3,18,18, 2,18, 2,18, 3, 2,18} },  // 244
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 245
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 246
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 247
        { 1, 1, { 3,17,18, 5, 2, 6, 7, 1, 4, 8} },  // 248
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 249
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 250
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 251
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 252
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 253
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 254
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2, 8} },  // 255
};

static const WavpackDecorrSpec very_high_specs [] = {
        { 1, 2, {18,18, 2, 3,-2,18, 2, 4, 7, 5, 3, 6, 8,-1,18, 2} },        // 0
        { 0, 1, {18,18,-1,18, 2, 3, 4, 6, 5, 7,18,-3, 8, 2,-1, 3} },        // 1
        { 1, 2, { 1,18,-2, 4,18, 2, 3, 6,-1, 7, 5,-2,18, 8, 2, 4} },        // 2
        { 0, 1, {17,17, 2, 3, 4,18,-1, 5, 6, 7,18, 2, 8,17, 3,-2} },        // 3
        { 1, 1, {18,18, 2,18, 3, 2,18, 4,-1, 3,18, 2, 6, 8,17, 5} },        // 4
        { 0, 2, {18,17, 2, 3,-2, 5,18,-3, 2, 4, 7, 3, 6, 8, 5,17} },        // 5
        { 1, 1, {18,-2, 2,-3,18, 5,-2,18, 2, 3, 6, 2,17, 4, 7,-1} },        // 6
        { 1, 1, {17, 8,18, 3,-2, 2, 5, 4,18, 6, 3, 8, 7, 2, 5, 4} },        // 7
        { 0, 2, {18,17,-2, 2,18, 3, 2, 5,-3, 4, 7,18, 3, 8, 6, 2} },        // 8
        { 1, 1, { 3, 6, 5, 5, 1, 3, 7, 4, 2, 6, 4,18, 3, 7, 5, 6} },        // 9
        { 1, 2, { 1,18, 3, 2,-2, 1, 5, 4, 6, 2, 7, 1, 8, 3,-1, 1} },        // 10
        { 0, 1, {18,18, 2, 3, 6, 3, 5,-2, 2, 4,18, 3,-2,-1, 6, 7} },        // 11
        { 0, 1, {-2,18, 2,18, 7, 2, 6,-2, 3, 4,18,18, 2,-3, 8, 5} },        // 12
        { 0, 2, {18,18,18, 2, 4, 3,18, 5, 3, 6,-2, 2, 4,18, 8, 7} },        // 13
        { 0, 1, {-2, 1,18, 2,-2,18,-1, 5, 7, 2, 3, 4,18, 2, 6, 2} },        // 14
        { 1, 1, {17,18, 3, 2, 1, 7,-1, 2, 4, 3, 5, 6,-2,18, 7, 8} },        // 15
        { 1, 1, {18,18, 2,18, 3, 4, 6,-2,18, 5, 8, 2, 3, 7, 4,-1} },        // 16
        { 0, 1, {18,18,18,-1, 2, 3, 4, 6, 8,18, 3, 5, 2, 6, 7, 4} },        // 17
        { 1, 1, {17,-2,18,18, 2, 5, 3, 8, 2,-1, 6, 1, 3, 4, 7, 5} },        // 18
        { 0, 1, {17,17,18, 2, 3, 6,-2, 8, 1, 7, 5, 2, 3, 1, 4, 8} },        // 19
        { 1, 1, {17,17, 3, 2, 7, 1, 4, 3, 6, 2, 5,-2, 8, 7,18, 6} },        // 20
        { 0, 1, {18,17,-2, 2,18, 3,-3, 7, 6, 5, 2, 4,-1, 8, 3,17} },        // 21
        { 1, 1, { 2,18,18,-2, 2, 4,-1, 5,18, 3, 8, 6, 2, 7,17, 4} },        // 22
        { 0, 1, {17, 3, 6, 8, 5, 4, 3, 8, 1,18, 7, 2, 4, 5, 6, 3} },        // 23
        { 1, 2, {17,18, 4, 8, 3, 2, 5, 7, 6, 8, 2, 7,-2,18, 3, 4} },        // 24
        { 1, 1, { 6, 5, 5, 3, 4, 7, 3, 2, 4, 6, 3, 7, 1, 5, 2, 4} },        // 25
        { 1, 1, { 1,18,-1, 2, 1, 3, 8,-2, 2, 5, 6, 3, 8, 7,18, 4} },        // 26
        { 0, 1, { 1,17,-1,18, 3, 2, 5, 4, 6, 7, 8, 3, 4, 2, 1,-2} },        // 27
        { 0, 1, {18, 2,18,18, 2,18, 6,-2,18, 7, 5, 4, 3, 2,18,-2} },        // 28
        { 0, 3, { 1, 4,18, 3, 2, 4, 1, 5, 2, 3, 6,18, 8, 7, 2, 4} },        // 29
        { 0, 1, {17,-2, 1,-3, 2,18, 3,-2, 4,18, 3, 6, 7,-3, 2, 8} },        // 30
        { 1, 1, {17,18,18, 4, 2, 3, 7, 6,18, 8, 5,-1, 4, 2, 3,17} },        // 31
        { 1, 2, {18,-1,17,18, 2, 3,-2,18, 5, 8, 2, 4, 3, 7, 6,-1} },        // 32
        { 1, 1, {18,18,18,-2, 4, 2, 3,18, 5, 8, 2, 4, 6, 7,-2, 3} },        // 33
        { 1, 2, {18,18,-2,18,-1, 3, 2, 5,18,-2, 7, 2, 3, 4, 6, 8} },        // 34
        { 0, 1, {17,18,-1, 2, 4,18, 8, 3, 6, 5, 7,-3, 2, 4, 3,17} },        // 35
        { 1, 1, {18,18,17, 2,-1,18, 3, 2,18, 6, 5, 4,18, 7, 2,-1} },        // 36
        { 0, 2, { 1,18,-1,18, 3, 2, 4, 6,-3, 7,-1, 5, 1, 2, 3, 8} },        // 37
        { 1, 1, { 1,17,-2, 2,-3, 6, 3, 5, 1, 2, 7, 6, 8,-2, 4, 1} },        // 38
        { 0, 1, {17,-1, 5, 1, 4, 3, 6, 2,-2,18, 3, 2, 4, 5, 8,-1} },        // 39
        { 0, 2, {18,18,17, 2, 3,-2, 5,18, 2, 4, 7, 8, 6,17, 3, 5} },        // 40
        { 1, 1, { 1, 5, 1, 3, 4, 3, 7, 5, 1, 3, 6, 1, 2, 4, 3, 8} },        // 41
        { 1, 2, { 1,-1, 3, 2,18, 7,-2, 5, 2, 6, 4, 3,-1,18, 8, 7} },        // 42
        { 0, 2, {18,17, 3,18, 2, 5, 4, 3, 6, 2, 7, 8,18, 3, 4, 5} },        // 43
        { 1, 1, { 3, 6,17, 8, 7, 5,18,-1, 1, 2, 3, 4, 2, 6, 8, 1} },        // 44
        { 0, 2, {18,18, 3,-3,18, 2, 6, 5, 3, 7,18, 4,-2, 8, 2, 3} },        // 45
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 46
        { 1, 1, {17, 1, 7, 2, 3,18,-2, 3, 6, 4, 2, 7, 8, 5, 3,17} },        // 47
        { 1, 1, { 3, 6, 5, 5, 1, 3, 7, 4, 2, 6, 4,18, 3, 7, 5, 6} },        // 48
        { 0, 1, {18,18,18, 2, 4,-1,18, 8,-1, 2, 3, 4, 6,-2, 1, 7} },        // 49
        { 1, 1, {18,-2,17,18, 2, 6, 3,-2, 5, 4, 7, 1,-3, 8, 2, 6} },        // 50
        { 0, 1, {17,18,18, 4, 2, 7, 3, 6,-2,18, 8, 4, 5, 2, 7,17} },        // 51
        { 1, 1, {18,18, 5, 4, 6, 4, 1, 5, 4, 3, 2, 5, 6, 1, 4, 5} },        // 52
        { 0, 1, {18,18,-2,18, 2,-3, 3, 8, 5,18, 6, 4, 3,-1, 7, 2} },        // 53
        { 1, 1, {18, 2,-2,-3,18, 5, 2, 3,-2, 4, 6, 1,-3, 2, 7, 8} },        // 54
        { 0, 1, {18, 3, 5, 8, 2, 6, 7, 3, 1, 5, 2,-1, 8, 6, 7, 4} },        // 55
        { 1, 1, { 4, 3, 8, 1, 5, 6, 2, 5, 8,-2, 2, 7, 3,18, 5, 4} },        // 56
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 57
        { 1, 1, {17, 3,18,18, 7, 2, 4,18, 6, 2, 3,-1, 8, 5,18,-3} },        // 58
        { 0, 1, { 3,17,18, 2,18, 6, 7,-3,18, 2, 5, 6, 3, 8, 7,-1} },        // 59
        { 1, 1, {18,18, 2,18,18, 2,-1, 7, 3,18, 5, 2, 6, 4,-1,18} },        // 60
        { 0, 3, {18, 3, 4, 1, 5, 2,18, 4, 2, 3,18, 7, 6, 1, 2, 4} },        // 61
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 62
        { 1, 1, {17, 1,18, 2, 3, 6, 4, 5, 7,18, 3, 8, 2, 4,-2,17} },        // 63
        { 1, 2, {18,17, 2, 3, 5,18, 6,-2, 7, 3, 2, 4,18, 8,-1, 5} },        // 64
        { 0, 2, { 1,18,-1,18, 3, 2, 4, 6,-3, 7,-1, 5, 1, 2, 3, 8} },        // 65
        { 1, 1, { 1,18,-1, 8, 2, 6, 3,-2, 1, 2, 5, 4,-3, 8, 6, 3} },        // 66
        { 0, 1, {18,18, 2,18, 2,18, 7, 6,18, 2,-2, 3, 5, 4,18, 8} },        // 67
        { 1, 2, {18,17, 2, 3,18,-1, 2, 3, 6,18, 5, 4, 3, 7, 2, 8} },        // 68
        { 1, 2, {18,18, 3,-2, 4,18, 5, 7, 6, 2, 4,-3, 8, 5,18, 3} },        // 69
        { 1, 1, {17,-2,18,18, 2, 5, 3, 8, 2,-1, 6, 1, 3, 4, 7, 5} },        // 70
        { 1, 1, { 3,17,18, 5, 7, 2, 4, 6, 1, 8,-1, 3, 7, 4, 1, 2} },        // 71
        { 0, 2, { 1,-2, 2,18, 3, 5, 2, 4, 7,-1, 2, 3, 5,18,-2, 4} },        // 72
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 73
        { 1, 1, { 1, 2,-2, 6,18,-3, 2, 7, 3,-2, 5, 6, 1, 8, 2, 4} },        // 74
        { 0, 1, {18,18,18, 3,-2, 6,18, 2, 4, 3, 5, 8, 7, 6, 2,-2} },        // 75
        { 1, 1, { 1, 5, 1, 3, 4, 3, 7, 5, 1, 3, 6, 1, 2, 4, 3, 8} },        // 76
        { 0, 1, { 3,17,18, 2, 5,18, 6, 7, 5,-2, 2, 4,18, 3, 6, 8} },        // 77
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 78
        { 0, 2, {17,-1,18, 2, 4,-1, 8, 3,18, 7,-3, 4, 5, 1, 2,-2} },        // 79
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 8, 6, 4, 5, 7,-1} },        // 80
        { 1, 1, {18,18, 3, 6, 4, 8,-2, 2, 5, 3, 7,18, 6, 8, 4, 2} },        // 81
        { 1, 1, {17,18,18,-2, 5, 2, 3, 1, 4,-1, 8, 6, 5, 3, 2,18} },        // 82
        { 1, 1, {17,17, 1, 2, 4, 5, 2, 6,-1, 3, 1, 1,-2, 4, 2, 7} },        // 83
        { 1, 1, {17, 1, 7, 2, 3,18,-2, 3, 6, 4, 2, 7, 8, 5, 3,17} },        // 84
        { 0, 1, {18,17,-2,-3, 1, 2, 3, 2, 5, 4, 7,-3, 6,-2, 2, 1} },        // 85
        { 1, 1, { 1, 3, 5,18, 1, 2, 7, 3, 6, 2, 5, 8,-1, 1, 4, 7} },        // 86
        { 1, 1, {17, 3, 6, 8, 1, 4, 5, 3,-2, 7, 2, 8, 5, 6,18, 3} },        // 87
        { 1, 1, {17,18, 2, 4, 8,-2, 3, 1, 5, 6, 7, 1, 2, 3, 4, 7} },        // 88
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 89
        { 1, 1, { 3, 1, 8,18, 5, 2, 3,18, 6, 7,-2, 4, 3, 2, 8,18} },        // 90
        { 0, 1, {18,17, 2,18, 3, 4,-1,18, 7, 6, 2, 8, 4,18,18, 5} },        // 91
        { 0, 1, {18,18, 2,18,18, 2, 7,-2, 6, 5, 4, 3,18, 3, 2,17} },        // 92
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 93
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 94
        { 1, 1, {17, 8,18, 3, 2, 1, 5, 4, 6,-1, 3,-3, 8,18, 7, 2} },        // 95
        { 1, 2, {18,17,18, 2, 3, 5,-2,18, 6,-1, 2, 3, 7, 4, 8,17} },        // 96
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 8, 6, 4, 5, 7,-1} },        // 97
        { 1, 2, {18,18,-2,17, 2,18, 3, 4,18, 8, 7,-1, 2, 4, 5,17} },        // 98
        { 0, 2, {17,-3,17, 3, 2,-2,18, 8, 4,-3, 2,18, 5, 3,-2, 6} },        // 99
        { 0, 1, {18,18, 2,18,18, 2, 7,-2, 6, 5, 4, 3,18, 3, 2,17} },        // 100
        { 0, 2, { 1,18,-1, 3, 5, 2,-3,18, 7, 3,-1, 6, 4, 2,17, 5} },        // 101
        { 1, 1, {17,-2,17, 2,-3, 1, 5,-1, 4, 6, 3, 2, 8, 7,-2, 5} },        // 102
        { 1, 1, { 1,18, 1, 3, 5, 8, 6, 2, 3,-1, 7, 1, 4, 8, 5,-3} },        // 103
        { 0, 2, { 3,18,18, 2,18,-2, 6, 5, 7, 2, 4,18, 3, 6,-3, 5} },        // 104
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 105
        { 1, 1, { 3, 6,17, 8, 7, 5,18,-1, 1, 2, 3, 4, 2, 6, 8, 1} },        // 106
        { 0, 4, {18, 2,17, 3,18,-2, 2, 6,18, 2, 7, 3, 5, 4, 8,18} },        // 107
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 108
        { 0, 1, {18,18, 2, 3, 6, 3, 5,-2, 2, 4,18, 3,-2,-1, 6, 7} },        // 109
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 110
        { 1, 1, {17, 1, 2, 5, 3,-2, 1, 4, 3, 7, 6,-3, 2, 1, 1, 2} },        // 111
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 112
        { 1, 1, {18,18,-2,18,-2, 2, 3, 6,18, 4,-1, 2, 3, 8, 1, 4} },        // 113
        { 1, 1, {17,-2,17, 2,-3, 1, 5,-1, 4, 6, 3, 2, 8, 7,-2, 5} },        // 114
        { 0, 1, {17,17,18, 3, 2,18,18, 6, 8, 2,-2, 3, 5, 4,17,18} },        // 115
        { 1, 1, { 1, 5, 1, 3, 4, 3, 7, 5, 1, 3, 6, 1, 2, 4, 3, 8} },        // 116
        { 1, 1, { 1, 3,-3,18,18, 6, 5,18, 2,-1, 3, 8, 7,-3, 4,17} },        // 117
        { 1, 1, {18, 1, 2, 1, 3, 8, 7, 4, 1, 5, 2,-1,-3,18, 6, 2} },        // 118
        { 0, 1, {18, 3, 5, 2, 6, 8,18, 5, 7, 2, 3,-1, 6, 7, 8, 5} },        // 119
        { 0, 2, {18, 3,-2, 7, 8, 2, 5, 4,-3, 8, 3, 2,18, 5, 4, 6} },        // 120
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 121
        { 1, 3, { 1, 1, 2, 5, 2, 7, 4, 3,-1,18,-2, 8, 2, 1, 6, 7} },        // 122
        { 0, 1, { 3,17,18, 5, 2, 6, 7,18, 4, 5, 3, 6,18, 2, 7, 8} },        // 123
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 124
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 125
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 126
        { 0, 1, { 1,18, 1, 2, 3, 5, 1, 2, 6, 7, 4, 3, 8, 1,17, 5} },        // 127
        { 1, 2, {17,-1,18,-2, 2, 3, 5,18, 2, 4, 6, 7, 3,-1, 5, 8} },        // 128
        { 1, 1, {18,18,-3,18,-2, 2, 3,-2,18, 6, 4, 5, 8, 3,17,-3} },        // 129
        { 1, 1, {18, 7, 6, 5, 5, 3, 1, 4, 2, 7, 3, 4,-3, 6,18, 8} },        // 130
        { 0, 2, {18,18, 2, 3, 5,18, 2, 4, 3, 6,18, 7, 8,-1, 5, 2} },        // 131
        { 0, 1, {18,17,-1, 2,18, 3, 2,18, 4, 3,18, 2, 6, 5, 8,17} },        // 132
        { 0, 2, {18,17, 2, 3,18, 5,-1, 6, 7, 8, 2, 3, 4, 5,18, 6} },        // 133
        { 1, 2, {18,-3,18, 2, 3,-2,-3, 5,18, 7, 6, 2, 4, 3, 8,-2} },        // 134
        { 1, 1, {17,18,18,-2, 2, 3, 5, 4, 8,18,-1, 5, 3, 6,-2, 7} },        // 135
        { 1, 2, {18,17, 2,-2,18, 3,-1, 4,18, 2, 7, 5, 3, 8, 6, 4} },        // 136
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 137
        { 1, 1, { 1, 5, 1, 3, 4, 3, 7, 5, 1, 3, 6, 1, 2, 4, 3, 8} },        // 138
        { 0, 2, {18,18, 3, 3,-2, 2, 5,18, 6, 3,-1, 4, 7,-1, 1, 2} },        // 139
        { 0, 1, {-2, 1,18, 2,-2, 5, 7,18, 3, 2, 6, 2,-1, 4,-2,17} },        // 140
        { 0, 2, {18,18,18, 2, 3,-2,18, 5, 4, 2, 6, 8, 3,-2, 4,18} },        // 141
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 142
        { 1, 1, {17,18,-1, 3, 2, 5, 1, 3, 2, 8, 4, 7, 6, 2,-1, 5} },        // 143
        { 1, 1, {17,18,18, 4, 2, 3, 7, 6,18, 8, 5,-1, 4, 2, 3,17} },        // 144
        { 0, 1, {18,18,-2,18, 2, 3, 4, 5, 6,18, 8, 2, 3, 7,-2, 4} },        // 145
        { 0, 1, {18,-2,18,18,-3,-2, 2, 3, 5, 8, 1, 2, 6, 4, 7,-1} },        // 146
        { 0, 1, {18,17, 2,18, 3,-2, 2, 7, 6, 4,18, 3, 8, 7, 4, 2} },        // 147
        { 1, 1, {17,18,18, 4, 2, 3, 7, 6,18, 8, 5,-1, 4, 2, 3,17} },        // 148
        { 1, 1, {18,17,18, 2, 5, 3,-2,18, 6, 2, 3, 4, 8, 7, 5,-1} },        // 149
        { 0, 1, { 2,-1,18,-1, 2, 4,-3,18, 5, 3, 6,18, 2, 4, 7, 8} },        // 150
        { 1, 1, {17,18, 8, 3, 6, 4,-1, 5, 2, 7, 3, 8, 6, 5,18, 4} },        // 151
        { 0, 2, {18, 3,-2, 7, 8, 2, 5, 4,-3, 8, 3, 2,18, 5, 4, 6} },        // 152
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 153
        { 1, 1, { 1,18,-1, 8, 2, 6, 3,-2, 1, 2, 5, 4,-3, 8, 6, 3} },        // 154
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 155
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 156
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 157
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 158
        { 0, 1, {17,18,18, 4, 2, 7, 3, 6,-2,18, 8, 4, 5, 2, 7,17} },        // 159
        { 1, 2, {18,-1,18, 3,-2,18, 2, 5, 3, 6, 7, 2,-1,18, 8, 4} },        // 160
        { 1, 2, { 1,18,-2, 4,18, 2, 3, 6,-1, 7, 5,-2,18, 8, 2, 4} },        // 161
        { 1, 2, { 1,18,-3, 2, 3,18,-1, 5, 6, 2, 8, 3, 4, 1,-2, 7} },        // 162
        { 0, 1, { 1,17,-1,18, 3, 2, 5, 4, 6, 7, 8, 3, 4, 2, 1,-2} },        // 163
        { 1, 1, {18,17,18, 4, 3, 5, 1, 2, 6, 3, 4, 7, 1, 8, 5, 2} },        // 164
        { 0, 1, {18,-2, 7, 1, 3, 2,-3, 4, 6,-2, 7, 8, 1, 5, 4, 3} },        // 165
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 166
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 167
        { 0, 2, {18,18,18,-2, 2, 5, 3, 7,18, 2, 4,-3, 5, 6, 3, 8} },        // 168
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 169
        { 0, 3, { 3,18,-1, 5, 2, 7,18, 6, 5, 2, 4, 3,-1, 7,18, 6} },        // 170
        { 0, 2, {18,18,18, 4, 3, 2, 6, 4, 8,18, 5, 3, 2, 7,-2, 6} },        // 171
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 172
        { 0, 2, {18,18,18, 2, 3,-2,18, 5, 4, 2, 6, 8, 3,-2, 4,18} },        // 173
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 174
        { 1, 1, {17, 8,18, 3, 2, 1, 5, 4, 6,-1, 3,-3, 8,18, 7, 2} },        // 175
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 176
        { 0, 1, {-1,18,18,18, 2, 4, 6,-2, 2, 8, 3, 4,18, 7,-1, 6} },        // 177
        { 0, 1, {18, 1,-2, 2, 4, 1, 3,-1, 2, 5, 7, 1, 6, 8,-2,17} },        // 178
        { 0, 1, {17,17,18, 2, 5, 4,18, 3, 8, 7, 4, 6, 8, 1, 5, 2} },        // 179
        { 1, 2, {18,18, 5, 4, 6, 3, 4,18, 8, 4,-1, 7, 5, 3, 6, 2} },        // 180
        { 0, 1, {18,18,-3,18, 3, 6, 2, 5, 7,18, 3, 8,-1, 4, 5, 2} },        // 181
        { 1, 1, {18, 2,-2,-3,18, 5, 2,-2, 4, 3, 6,18, 8,-1, 2, 7} },        // 182
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 183
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 184
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 185
        { 1, 1, {17, 1, 7, 2, 3,18,-2, 3, 6, 4, 2, 7, 8, 5, 3,17} },        // 186
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 187
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 188
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 189
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 190
        { 0, 1, {17,18, 3,18, 2, 5, 4, 7,-3, 6, 3, 2,18, 4, 7, 3} },        // 191
        { 1, 1, { 1, 7, 4, 5, 3, 4, 5, 1, 3, 6, 3, 2, 4, 8,-2, 7} },        // 192
        { 0, 1, { 1,18,-1,-2,18, 3, 2,-1, 6, 7, 4, 5, 3,18, 2,-3} },        // 193
        { 1, 1, {18,18,-1, 3, 6,18, 5, 4, 8, 2, 3, 6,18, 7, 4,-2} },        // 194
        { 0, 2, {18,18, 2, 6,18, 2,18, 5, 3,18, 2, 4, 7, 8, 3,18} },        // 195
        { 1, 1, { 3,18,18, 5,18, 6, 2, 4, 7,-2,18, 5, 8, 6, 3, 2} },        // 196
        { 0, 1, {18,-2, 7, 1, 3, 2,-3, 4, 6,-2, 7, 8, 1, 5, 4, 3} },        // 197
        { 1, 1, {18,-2,18, 2, 5,18, 3,-2, 4, 7, 2,-1, 8, 6, 5, 1} },        // 198
        { 1, 1, {17,17, 5,18, 4, 1, 2, 8, 6, 4,-2, 3, 5,-1, 1, 8} },        // 199
        { 0, 2, { 1, 2,17, 3, 7,18, 2,-1, 4, 5,18, 2, 7, 3, 6, 8} },        // 200
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 201
        { 1, 1, { 3, 6,17, 8, 7, 5,18,-1, 1, 2, 3, 4, 2, 6, 8, 1} },        // 202
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 203
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 204
        { 0, 2, {18,18,18, 2,-2, 3, 6, 4, 8,18, 2, 5, 7, 4, 3, 6} },        // 205
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 206
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 207
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 208
        { 1, 1, {18, 1, 8, 3, 5, 6, 4,-1, 8, 3, 7,18, 2, 5, 8, 4} },        // 209
        { 1, 1, {17,18, 5, 2, 4, 3, 1, 6,-2, 1, 3, 2, 4, 5,-1,17} },        // 210
        { 1, 1, {18,17, 2,18, 3,-3, 7, 2, 6, 4, 3, 5,18, 8, 2,-2} },        // 211
        { 1, 1, {18,17,18, 4, 3, 5,-1,18, 2, 7, 8, 4, 6, 3,18, 5} },        // 212
        { 0, 1, {18,17,18,-2, 2,-3, 3, 4, 8, 5, 2,18, 6, 3, 7,-2} },        // 213
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 214
        { 1, 1, {17,18, 8, 3, 4, 6,18, 5,-2, 3, 8, 5, 2, 4, 7, 6} },        // 215
        { 0, 1, {18,-2, 3, 5, 1, 7, 3, 2, 6,-3, 4, 1, 5, 8, 3,-2} },        // 216
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 217
        { 1, 1, { 3,17,18, 5,-1,18, 2, 6, 7,18, 5, 3,-3,-1, 6, 2} },        // 218
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 219
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 220
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 221
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 222
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 223
        { 1, 3, {18,17,-2, 3,-1,18, 2, 5, 3, 7, 6, 2, 4, 8,18, 5} },        // 224
        { 0, 1, {18,-1,18, 2,18, 3, 5,18, 2, 8,18, 5, 4,-1, 6, 2} },        // 225
        { 1, 2, {18,-2,18,18, 2, 3, 4,-3, 2, 5,18, 7, 4, 3, 8, 6} },        // 226
        { 0, 2, {17,-1,18, 2,-1, 1, 7, 3, 8, 5,-2, 4, 1, 2,-3, 6} },        // 227
        { 0, 1, {18,17, 2,18, 2,18, 6, 7, 4, 3,18, 5, 2,-2,17, 8} },        // 228
        { 0, 3, {18,17, 2, 3,-3,-1,18, 2, 4, 5,18, 7, 3, 2,-3, 6} },        // 229
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 230
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 231
        { 0, 2, { 3,18,18,18, 2, 6, 5,18, 7, 2, 4, 6,18, 5, 3, 8} },        // 232
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 233
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 234
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 235
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 236
        { 0, 1, {18,18, 3, 6, 3,-2, 2,18, 5,-1, 7, 3, 4,-2, 2, 6} },        // 237
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 238
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 239
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 240
        { 1, 1, {18,17,18,18,-2, 2, 3,-3,18, 6, 4, 2,-2, 8, 3, 7} },        // 241
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 242
        { 0, 1, {18,18,18, 4, 2, 7, 8,18, 3, 2,-2, 4, 7, 6,17, 5} },        // 243
        { 1, 1, {18,18,-1,-2, 8, 3,18, 6, 3, 5, 8, 2, 4, 7, 1, 6} },        // 244
        { 1, 1, { 1,-3, 3,18,18, 2,-1, 3, 6, 5,18, 4, 7,-2, 8, 3} },        // 245
        { 1, 1, { 1,18, 4, 2, 5,18, 1, 3,-1, 6, 1, 4, 8, 2, 5, 1} },        // 246
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 247
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 248
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 249
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 250
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 251
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 252
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 253
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 254
        { 0, 1, {-1,18,18, 2,18, 3, 5,18, 2,18, 6, 8, 4, 5, 7,-1} },        // 255
};

#define NUM_FAST_SPECS (sizeof (fast_specs) / sizeof (fast_specs [0]))
#define NUM_DEFAULT_SPECS (sizeof (default_specs) / sizeof (default_specs [0]))
#define NUM_HIGH_SPECS (sizeof (high_specs) / sizeof (high_specs [0]))
#define NUM_VERY_HIGH_SPECS (sizeof (very_high_specs) / sizeof (very_high_specs [0]))
