# exports file for Watcom
++'WavpackOpenRawDecoder'.'wavpack.dll'.'WavpackOpenRawDecoder'
++'WavpackOpenFileInputEx64'.'wavpack.dll'.'WavpackOpenFileInputEx64'
++'WavpackOpenFileInputEx'.'wavpack.dll'.'WavpackOpenFileInputEx'
++'WavpackOpenFileInput'.'wavpack.dll'.'WavpackOpenFileInput'
++'WavpackGetMode'.'wavpack.dll'.'WavpackGetMode'
++'WavpackVerifySingleBlock'.'wavpack.dll'.'WavpackVerifySingleBlock'
++'WavpackGetQualifyMode'.'wavpack.dll'.'WavpackGetQualifyMode'
++'WavpackGetErrorMessage'.'wavpack.dll'.'WavpackGetErrorMessage'
++'WavpackGetVersion'.'wavpack.dll'.'WavpackGetVersion'
++'WavpackGetFileExtension'.'wavpack.dll'.'WavpackGetFileExtension'
++'WavpackGetFileFormat'.'wavpack.dll'.'WavpackGetFileFormat'
++'WavpackUnpackSamples'.'wavpack.dll'.'WavpackUnpackSamples'
++'WavpackGetNumSamples'.'wavpack.dll'.'WavpackGetNumSamples'
++'WavpackGetNumSamples64'.'wavpack.dll'.'WavpackGetNumSamples64'
++'WavpackGetNumSamplesInFrame'.'wavpack.dll'.'WavpackGetNumSamplesInFrame'
++'WavpackGetSampleIndex'.'wavpack.dll'.'WavpackGetSampleIndex'
++'WavpackGetSampleIndex64'.'wavpack.dll'.'WavpackGetSampleIndex64'
++'WavpackGetNumErrors'.'wavpack.dll'.'WavpackGetNumErrors'
++'WavpackLossyBlocks'.'wavpack.dll'.'WavpackLossyBlocks'
++'WavpackSeekSample'.'wavpack.dll'.'WavpackSeekSample'
++'WavpackSeekSample64'.'wavpack.dll'.'WavpackSeekSample64'
++'WavpackCloseFile'.'wavpack.dll'.'WavpackCloseFile'
++'WavpackGetSampleRate'.'wavpack.dll'.'WavpackGetSampleRate'
++'WavpackGetNativeSampleRate'.'wavpack.dll'.'WavpackGetNativeSampleRate'
++'WavpackGetBitsPerSample'.'wavpack.dll'.'WavpackGetBitsPerSample'
++'WavpackGetBytesPerSample'.'wavpack.dll'.'WavpackGetBytesPerSample'
++'WavpackGetNumChannels'.'wavpack.dll'.'WavpackGetNumChannels'
++'WavpackGetChannelMask'.'wavpack.dll'.'WavpackGetChannelMask'
++'WavpackGetReducedChannels'.'wavpack.dll'.'WavpackGetReducedChannels'
++'WavpackGetFloatNormExp'.'wavpack.dll'.'WavpackGetFloatNormExp'
++'WavpackGetMD5Sum'.'wavpack.dll'.'WavpackGetMD5Sum'
++'WavpackGetChannelIdentities'.'wavpack.dll'.'WavpackGetChannelIdentities'
++'WavpackGetChannelLayout'.'wavpack.dll'.'WavpackGetChannelLayout'
++'WavpackGetWrapperBytes'.'wavpack.dll'.'WavpackGetWrapperBytes'
++'WavpackGetWrapperData'.'wavpack.dll'.'WavpackGetWrapperData'
++'WavpackFreeWrapper'.'wavpack.dll'.'WavpackFreeWrapper'
++'WavpackSeekTrailingWrapper'.'wavpack.dll'.'WavpackSeekTrailingWrapper'
++'WavpackGetProgress'.'wavpack.dll'.'WavpackGetProgress'
++'WavpackGetFileSize'.'wavpack.dll'.'WavpackGetFileSize'
++'WavpackGetFileSize64'.'wavpack.dll'.'WavpackGetFileSize64'
++'WavpackGetRatio'.'wavpack.dll'.'WavpackGetRatio'
++'WavpackGetAverageBitrate'.'wavpack.dll'.'WavpackGetAverageBitrate'
++'WavpackGetInstantBitrate'.'wavpack.dll'.'WavpackGetInstantBitrate'
++'WavpackGetNumTagItems'.'wavpack.dll'.'WavpackGetNumTagItems'
++'WavpackGetTagItem'.'wavpack.dll'.'WavpackGetTagItem'
++'WavpackGetTagItemIndexed'.'wavpack.dll'.'WavpackGetTagItemIndexed'
++'WavpackGetNumBinaryTagItems'.'wavpack.dll'.'WavpackGetNumBinaryTagItems'
++'WavpackGetBinaryTagItem'.'wavpack.dll'.'WavpackGetBinaryTagItem'
++'WavpackGetBinaryTagItemIndexed'.'wavpack.dll'.'WavpackGetBinaryTagItemIndexed'
++'WavpackAppendTagItem'.'wavpack.dll'.'WavpackAppendTagItem'
++'WavpackAppendBinaryTagItem'.'wavpack.dll'.'WavpackAppendBinaryTagItem'
++'WavpackDeleteTagItem'.'wavpack.dll'.'WavpackDeleteTagItem'
++'WavpackWriteTag'.'wavpack.dll'.'WavpackWriteTag'
++'WavpackOpenFileOutput'.'wavpack.dll'.'WavpackOpenFileOutput'
++'WavpackSetFileInformation'.'wavpack.dll'.'WavpackSetFileInformation'
++'WavpackSetConfiguration'.'wavpack.dll'.'WavpackSetConfiguration'
++'WavpackSetConfiguration64'.'wavpack.dll'.'WavpackSetConfiguration64'
++'WavpackSetChannelLayout'.'wavpack.dll'.'WavpackSetChannelLayout'
++'WavpackAddWrapper'.'wavpack.dll'.'WavpackAddWrapper'
++'WavpackStoreMD5Sum'.'wavpack.dll'.'WavpackStoreMD5Sum'
++'WavpackPackInit'.'wavpack.dll'.'WavpackPackInit'
++'WavpackPackSamples'.'wavpack.dll'.'WavpackPackSamples'
++'WavpackFlushSamples'.'wavpack.dll'.'WavpackFlushSamples'
++'WavpackUpdateNumSamples'.'wavpack.dll'.'WavpackUpdateNumSamples'
++'WavpackGetWrapperLocation'.'wavpack.dll'.'WavpackGetWrapperLocation'
++'WavpackGetEncodedNoise'.'wavpack.dll'.'WavpackGetEncodedNoise'
++'WavpackFloatNormalize'.'wavpack.dll'.'WavpackFloatNormalize'
++'WavpackLittleEndianToNative'.'wavpack.dll'.'WavpackLittleEndianToNative'
++'WavpackNativeToLittleEndian'.'wavpack.dll'.'WavpackNativeToLittleEndian'
++'WavpackBigEndianToNative'.'wavpack.dll'.'WavpackBigEndianToNative'
++'WavpackNativeToBigEndian'.'wavpack.dll'.'WavpackNativeToBigEndian'
++'WavpackGetLibraryVersion'.'wavpack.dll'.'WavpackGetLibraryVersion'
++'WavpackGetLibraryVersionString'.'wavpack.dll'.'WavpackGetLibraryVersionString'
