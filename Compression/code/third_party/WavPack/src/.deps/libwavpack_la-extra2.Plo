src/libwavpack_la-extra2.lo: src/extra2.c /usr/include/stdc-predef.h \
 /usr/include/stdlib.h /usr/include/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/sys/cdefs.h \
 /usr/include/bits/wordsize.h /usr/include/bits/long-double.h \
 /usr/include/gnu/stubs.h /usr/include/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stddef.h \
 /usr/include/bits/waitflags.h /usr/include/bits/waitstatus.h \
 /usr/include/bits/floatn.h /usr/include/bits/floatn-common.h \
 /usr/include/sys/types.h /usr/include/bits/types.h \
 /usr/include/bits/typesizes.h /usr/include/bits/types/clock_t.h \
 /usr/include/bits/types/clockid_t.h /usr/include/bits/types/time_t.h \
 /usr/include/bits/types/timer_t.h /usr/include/bits/stdint-intn.h \
 /usr/include/endian.h /usr/include/bits/endian.h \
 /usr/include/bits/byteswap.h /usr/include/bits/uintn-identity.h \
 /usr/include/sys/select.h /usr/include/bits/select.h \
 /usr/include/bits/types/sigset_t.h /usr/include/bits/types/__sigset_t.h \
 /usr/include/bits/types/struct_timeval.h \
 /usr/include/bits/types/struct_timespec.h \
 /usr/include/bits/pthreadtypes.h /usr/include/bits/thread-shared-types.h \
 /usr/include/bits/pthreadtypes-arch.h /usr/include/alloca.h \
 /usr/include/bits/stdlib-bsearch.h /usr/include/bits/stdlib-float.h \
 /usr/include/stdio.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdarg.h \
 /usr/include/bits/types/__fpos_t.h /usr/include/bits/types/__mbstate_t.h \
 /usr/include/bits/types/__fpos64_t.h /usr/include/bits/types/__FILE.h \
 /usr/include/bits/types/FILE.h /usr/include/bits/types/struct_FILE.h \
 /usr/include/bits/stdio_lim.h /usr/include/bits/sys_errlist.h \
 /usr/include/bits/stdio.h /usr/include/string.h \
 /usr/include/bits/types/locale_t.h /usr/include/bits/types/__locale_t.h \
 /usr/include/strings.h /usr/include/math.h \
 /usr/include/bits/math-vector.h /usr/include/bits/libm-simd-decl-stubs.h \
 /usr/include/bits/flt-eval-method.h /usr/include/bits/fp-logb.h \
 /usr/include/bits/fp-fast.h \
 /usr/include/bits/mathcalls-helper-functions.h \
 /usr/include/bits/mathcalls.h /usr/include/bits/mathinline.h \
 src/wavpack_local.h include/wavpack.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdint.h \
 /usr/include/stdint.h /usr/include/bits/wchar.h \
 /usr/include/bits/stdint-uintn.h /usr/include/pthread.h \
 /usr/include/sched.h /usr/include/bits/sched.h \
 /usr/include/bits/types/struct_sched_param.h /usr/include/bits/cpu-set.h \
 /usr/include/time.h /usr/include/bits/time.h \
 /usr/include/bits/types/struct_tm.h \
 /usr/include/bits/types/struct_itimerspec.h /usr/include/bits/setjmp.h

/usr/include/stdc-predef.h:

/usr/include/stdlib.h:

/usr/include/bits/libc-header-start.h:

/usr/include/features.h:

/usr/include/sys/cdefs.h:

/usr/include/bits/wordsize.h:

/usr/include/bits/long-double.h:

/usr/include/gnu/stubs.h:

/usr/include/gnu/stubs-64.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stddef.h:

/usr/include/bits/waitflags.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/floatn.h:

/usr/include/bits/floatn-common.h:

/usr/include/sys/types.h:

/usr/include/bits/types.h:

/usr/include/bits/typesizes.h:

/usr/include/bits/types/clock_t.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/bits/types/time_t.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/stdint-intn.h:

/usr/include/endian.h:

/usr/include/bits/endian.h:

/usr/include/bits/byteswap.h:

/usr/include/bits/uintn-identity.h:

/usr/include/sys/select.h:

/usr/include/bits/select.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/bits/types/struct_timeval.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/alloca.h:

/usr/include/bits/stdlib-bsearch.h:

/usr/include/bits/stdlib-float.h:

/usr/include/stdio.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdarg.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/bits/types/__FILE.h:

/usr/include/bits/types/FILE.h:

/usr/include/bits/types/struct_FILE.h:

/usr/include/bits/stdio_lim.h:

/usr/include/bits/sys_errlist.h:

/usr/include/bits/stdio.h:

/usr/include/string.h:

/usr/include/bits/types/locale_t.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/strings.h:

/usr/include/math.h:

/usr/include/bits/math-vector.h:

/usr/include/bits/libm-simd-decl-stubs.h:

/usr/include/bits/flt-eval-method.h:

/usr/include/bits/fp-logb.h:

/usr/include/bits/fp-fast.h:

/usr/include/bits/mathcalls-helper-functions.h:

/usr/include/bits/mathcalls.h:

/usr/include/bits/mathinline.h:

src/wavpack_local.h:

include/wavpack.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdint.h:

/usr/include/stdint.h:

/usr/include/bits/wchar.h:

/usr/include/bits/stdint-uintn.h:

/usr/include/pthread.h:

/usr/include/sched.h:

/usr/include/bits/sched.h:

/usr/include/bits/types/struct_sched_param.h:

/usr/include/bits/cpu-set.h:

/usr/include/time.h:

/usr/include/bits/time.h:

/usr/include/bits/types/struct_tm.h:

/usr/include/bits/types/struct_itimerspec.h:

/usr/include/bits/setjmp.h:
