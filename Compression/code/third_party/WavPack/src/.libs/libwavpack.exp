WavpackAddWrapper
WavpackAppendBinaryTagItem
WavpackAppendTagItem
WavpackBigEndianToNative
WavpackCloseFile
WavpackDeleteTagItem
WavpackFloatNormalize
WavpackFlushSamples
WavpackFreeWrapper
WavpackGetAverageBitrate
WavpackGetBinaryTagItem
WavpackGetBinaryTagItemIndexed
WavpackGetBitsPerSample
WavpackGetBytesPerSample
WavpackGetChannelIdentities
WavpackGetChannelLayout
WavpackGetChannelMask
WavpackGetEncodedNoise
WavpackGetErrorMessage
WavpackGetFileExtension
WavpackGetFileFormat
WavpackGetFileSize
WavpackGetFileSize64
WavpackGetFloatNormExp
WavpackGetInstantBitrate
WavpackGetLibraryVersion
WavpackGetLibraryVersionString
WavpackGetMD5Sum
WavpackGetMode
WavpackGetNativeSampleRate
WavpackGetNumBinaryTagItems
WavpackGetNumChannels
WavpackGetNumErrors
WavpackGetNumSamples
WavpackGetNumSamples64
WavpackGetNumSamplesInFrame
WavpackGetNumTagItems
WavpackGetProgress
WavpackGetQualifyMode
WavpackGetRatio
WavpackGetReducedChannels
WavpackGetSampleIndex
WavpackGetSampleIndex64
WavpackGetSampleRate
WavpackGetTagItem
WavpackGetTagItemIndexed
WavpackGetVersion
WavpackGetWrapperBytes
WavpackGetWrapperData
WavpackGetWrapperLocation
WavpackLittleEndianToNative
WavpackLossyBlocks
WavpackNativeToBigEndian
WavpackNativeToLittleEndian
WavpackOpenFileInput
WavpackOpenFileInputEx
WavpackOpenFileInputEx64
WavpackOpenFileOutput
WavpackOpenRawDecoder
WavpackPackInit
WavpackPackSamples
WavpackSeekSample
WavpackSeekSample64
WavpackSeekTrailingWrapper
WavpackSetChannelLayout
WavpackSetConfiguration
WavpackSetConfiguration64
WavpackSetFileInformation
WavpackStoreMD5Sum
WavpackUnpackSamples
WavpackUpdateNumSamples
WavpackVerifySingleBlock
WavpackWriteTag
