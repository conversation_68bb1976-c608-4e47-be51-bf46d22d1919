.Dd February 9, 2024
.Dt WAVPACK 1
.Os
.Sh NAME
.Nm wavpack
.Nd encode audio files to wavpack
.Sh SYNOPSIS
.Nm wavpack
.Op Fl options
.Op Fl o Ar output
.Ar
.Sh DESCRIPTION
.Nm wavpack
encodes the specified source files into WavPack files.
The source files may be any of the file format types listed below,
and the audio data may be either uncompressed PCM or DSD,
depending on the format.
Raw audio may also be specified (see
.Fl -raw-pcm ) .
.Pp
Unless overridden with the
.Fl o
option, the output filename will be the source filename
with the filename extension replaced by
.Dq .wv .
Multiple input files may be specified,
resulting in multiple WavPack files,
and in that case
.Fl o
may be used to specify an alternate target directory.
A filename of
.Dq -
specifies
.Pa stdin
or
.Pa stdout .
.Pp
When transcoding from existing WavPack files,
all tags are copied, and may be modified with additional args;
unless an alternate output file or directory is specified,
the source files are safely overwritten.
.Ss INPUT FORMATS
.Bl -bullet -compact
.It
Microsoft RIFF, extension
.Dq .wav ,
including BWF and RF64 variants
.It
WavPack, extension
.Dq .wv ,
transcode operation, tags copied
.It
Apple AIFF, extension
.Dq .aif
.It
Apple Core Audio, extension
.Dq .caf
.It
Sony Wave64, extension
.Dq .w64
.It
Philips DSDIFF, extension
.Dq .dff
.It
Sony DSD Stream, extension
.Dq .dsf
.El
.Ss OPTIONS
.Bl -tag -width Ds
.It Fl a
Adobe Audition (CoolEdit) mode for 32-bit floats
.It Fl -allow-huge-tags
Allow tag data up to 16 MB.
Embedding > 1 MB is not recommended for portable devices
and may not work with some programs, including older WavPack versions.
.It Fl b Ns Ar n
Enable hybrid compression,
.Ar n
= 2.0 to 23.9 bits/sample, or
.Ar n
= 24-9600 kbits/second (kbps). If this option is specified
.Em without
the
.Fl c
option, then the operation is lossy.
The hybrid mode is not applicable to DSD audio (only PCM).
.It Fl -blocksize= Ns Ar n
specify block size in samples (max = 131072 and min = 16 with
.Fl -merge-blocks ,
otherwise 128)
.It Fl c
Create correction file (.wvc) for hybrid mode resulting in 2-file lossless
compression.
The bitrate is specified with the
.Fl b Ns Ar n
option.
.It Fl cc
maximum hybrid compression (hurts lossy quality & decode speed)
.It Fl c Ns Ar n
Enable hybrid lossless (i.e., 2-file) compression,
.Ar n
= 2.0 to 23.9 bits/sample, or
.Ar n
= 24-9600 kbits/second (kbps). This is a shortcut combining the
.Fl b Ns Ar n
and
.Fl c
options.
The hybrid mode is not applicable to DSD audio (only PCM).
.It Fl -blocksize= Ns Ar n
.It Fl -channel-order= Ns Ar list
Specify comma separated channel order if not the Microsoft standard,
which is FL,FR,FC,LFE,BL,BR,FLC,FRC,BC,SL,SR,TC,TFL,TFC,TFR,TBL,TBC,TBR;
specify
.Dq ...
to indicate that channels are not assigned to specific speakers,
or terminate list with
.Dq ...
to indicate that any channels beyond those specified are unassigned.
.It Fl -cross-decorr
Use cross-channel correlation in hybrid mode.
On by default in lossless mode and with the
.Fl cc
option.
.It Fl d
delete source file if successful;
.Sy use with caution!
.It Fl f
fast mode; introduces a compromise in compression ratio
.It Fl -force-even-byte-depth
Round the bit depth specified in the source file up
to the next whole byte (e.g., 20-bit is rounded up to 24-bit)
to handle cases where the value specified is not correct
(i.e., padding bits are non-zero).
.It Fl g
general/normal mode, cancels previously specified -f and -h options
.It Fl h
high quality; better compression ratio, but slightly slower
.It Fl hh
very high quality; best compression, but slowest;
not recommended on vintage playback devices
.It Fl -help
display extended help
.It Fl i
Ignore length in wav header and allow WAV files greater than 4 GB.
This will attempt to fix the WAV header stored in the WavPack file;
combine with
.Fl r
to create a fresh header instead.
.It Fl -import-id3
import applicable tag items from an ID3v2.3 or ID3v2.4 tag present
in DSF and other files into the APEv2 tag.
If there are > 1 MB cover images present, add
.Fl -allow-huge-tags
to include them, and
.Fl r
if you do not want large images appearing twice in the WavPack file,
although this will remove the entire original ID3v2 tag.
.It Fl j Ns Ar n
joint-stereo override (0 = left/right, 1 = mid/side)
.It Fl m
compute & store MD5 signature of raw audio data
.It Fl -merge-blocks
merge consecutive blocks with equal redundancy (used with
.Fl -blocksize
option and is useful for files generated
by the lossyWAV program or decoded HDCD files)
.It Fl n
calculate average and peak quantization noise
(hybrid only, reference fullscale sine)
.It Fl -no-overwrite
Never overwrite, nor ask to overwrite, an existing file.
This is handy for resuming a cancelled batch operation
and obviously cannot be mixed with the
.Fl y
option.
.It Fl -no-utf8-convert
Don't recode passed tags from local encoding to UTF-8,
assume they are in UTF-8 already.
.It Fl o Ar outfile
Specify an output filename for a single source file
or a target directory (must already exist) for multiple files.
.It Fl -optimize-int32
New optimization targeting 32-bit integer audio files that were originally
sourced from 32-bit float audio.
Can improve compression by 10%, but is only applicable in lossless modes.
Resulting files will only decode with 24-bit resolution on older decoders
(i.e., technically lossy).
.It Fl -pair-unassigned-chans
encode unassigned channels into stereo pairs
.It Fl -pre-quantize= Ns Ar bits
pre-quantize samples to
.Ar bits
depth BEFORE encoding and MD5 calculation
(common use would be
.Fl -pre-quantize=20
for 24-bit or float material recorded with typical converters)
.It Fl q
quiet (keep console output to a minimum)
.It Fl r
parse headers to determine audio format and length
but do not store the headers in the resulting WavPack file
(a minimum header will be generated by
.Nm wvunpack ,
but some non-audio metadata might be lost)
.It Fl -raw-pcm
input data is raw pcm (44,100 Hz, 16-bit, 2-channels)
.It Fl -raw-pcm= Ns Ar sr , Ns Ar bits Ns [f|s|u], Ns Ar chans , Ns [le|be]
Input data is raw pcm with specified sample-rate,
bit-depth (float, unsigned, signed), number of channels, and endianness.
Defaulted parameters may be omitted, specify
.Ar bits Ns =1
for DSD.
.It Fl -raw-pcm-skip= Ns Ar begin Ns [, Ns Ar end ]
skip
.Ar begin
bytes before encoding raw PCM (header) and skip
.Ar end
bytes at the EOF (trailer)
.It Fl s Ns Ar n
Override default hybrid mode noise shaping where
.Ar n
is a float value between -1.0 and 1.0.
Negative values move noise lower in freq,
positive values move noise higher in freq;
use 0 for no shaping (white noise).
.It Fl t
Copy input file's time stamp to output files.
.It Fl -threads= Ns Ar n
Enable multithreaded operation with
.Ar n=1
(no threading) to
.Ar n=12
(max threads).
Significantly improves performance of lossless compression on multicore CPUs.
In the hybrid modes, only multichannel files can utilize multithreading.
Omitting the parameter will select an optimum number of threads.
.It Fl -use-dns
force use of dynamic noise shaping (hybrid mode only)
.It Fl v
verify output file integrity after write (not for piped output)
.It Fl -version
write program version to
.Pa stdout
.It Fl w Encoder
write encoder metadata to APEv2 tag (e.g.,
.Dq Encoder=WavPack 5.6.0 )
.It Fl w Settings
write user settings metadata to APEv2 tag (e.g.,
.Dq Settings=-hb384cx3 )
.It Fl w Do Ar Field Ns = Ns Ar Value Dc
write the specified text metadata to APEv2 tag
.It Fl w Do Ar Field Ns =@ Ns Ar file.ext Dc
Write specified text metadata from file to APEv2 tag;
normally used for embedded cuesheets and logs
(field names
.Dq Cuesheet
and
.Dq Log ) .
.It Fl -write-binary-tag Do Ar Field Ns =@ Ns Ar file.ext Dc
Write the specified binary metadata file to APEv2 tag;
normally used for cover art with the field name
.Dq Cover Art (Front) .
.It Fl x[ Ns Ar n ]
extra encode processing, n = 0 to 6, default=1;
-x0 for no extra processing,
-x1 to -x3 to choose best of predefined filters,
-x4 to -x6 to generate custom filters (very slow!)
.It Fl y
yes to all warnings;
.Sy use with caution!
.It Fl z[ Ns Ar n ]
don't set (n=0 or omitted) or set (n=1) console title
to indicate progress (leaves "WavPack Completed")
.El
.Sh SEE ALSO
.Xr wvgain 1 ,
.Xr wvtag 1 ,
.Xr wvunpack 1 ,
.Lk www.wavpack.com
.Sh AUTHORS
.An David Bryant <NAME_EMAIL>
.An Sebastian Dröge <NAME_EMAIL>
.An Jan Starý <NAME_EMAIL>
