// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "winresrc.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""winresrc.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

COOLEDIT DIALOGEX 32, 32, 312, 161
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION
CAPTION "WavPack Configuration"
FONT 8, "MS Sans Serif", 0, 0, 0x0
BEGIN
    GROUPBOX        "Compression Mode",IDC_MODE_GROUP,5,5,80,150
    CONTROL         "Lossless",IDC_LOSSLESS,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,10,20,70,15
    CONTROL         "Lossless (high)",IDC_LOSSLESS_HIGH,"Button",BS_AUTORADIOBUTTON,10,35,70,15
    CONTROL         "Lossless (v-high)",IDC_LOSSLESS_VHIGH,"Button",BS_AUTORADIOBUTTON,10,50,70,15
    CONTROL         "Lossless (fast)",IDC_LOSSLESS_FAST,"Button",BS_AUTORADIOBUTTON,10,65,70,15
    CONTROL         "Hybrid",IDC_HYBRID,"Button",BS_AUTORADIOBUTTON,10,90,70,15
    CONTROL         "Hybrid (high)",IDC_HYBRID_HIGH,"Button",BS_AUTORADIOBUTTON,10,105,70,15
    CONTROL         "Hybrid (v-high)",IDC_HYBRID_VHIGH,"Button",BS_AUTORADIOBUTTON,10,120,70,15
    CONTROL         "Hybrid (fast)",IDC_HYBRID_FAST,"Button",BS_AUTORADIOBUTTON,10,135,70,15
    GROUPBOX        "Hybrid Specific",IDC_HYBRID_GROUP,95,5,105,61,WS_GROUP
    COMBOBOX        IDC_BITRATE,100,20,40,85,CBS_DROPDOWN | WS_VSCROLL | WS_TABSTOP
    LTEXT           "Bitrate in kbps",IDC_BITRATE_TEXT,145,20,50,15,SS_CENTERIMAGE
    CONTROL         "Create Correction File",IDC_CORRECTION,"Button",BS_AUTOCHECKBOX | BS_LEFT | WS_TABSTOP,100,40,95,15
    GROUPBOX        "32-bit Float Storage",IDC_FLOAT_GROUP,210,5,95,115
    CONTROL         "32-bit floats (type 1)",IDC_FLOAT32,"Button",BS_AUTORADIOBUTTON | WS_GROUP | WS_TABSTOP,215,20,80,15
    CONTROL         "Convert to 24-bit ints",IDC_FLOAT24,"Button",BS_AUTORADIOBUTTON,215,55,80,15
    CONTROL         "Convert to 20-bit ints",IDC_FLOAT20,"Button",BS_AUTORADIOBUTTON,215,70,80,15
    CONTROL         "Normalize (type 3)",IDC_NORMALIZE,"Button",BS_AUTOCHECKBOX | WS_GROUP | WS_TABSTOP,225,36,70,15
    CONTROL         "Noise Shaping",IDC_NOISESHAPE,"Button",BS_AUTOCHECKBOX | WS_GROUP | WS_TABSTOP,225,84,70,15
    CONTROL         "Dithering",IDC_DITHER,"Button",BS_AUTOCHECKBOX,225,100,70,15
    GROUPBOX        "Extra Processing",IDC_EXTRA_GROUP,95,72,105,48
    CONTROL         "",IDC_EXTRA_SLIDER,"msctls_trackbar32",TBS_AUTOTICKS | TBS_BOTH | TBS_TOOLTIPS | WS_TABSTOP,102,90,90,24
    DEFPUSHBUTTON   "OK",IDOK,96,132,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,156,132,50,14
    PUSHBUTTON      "About",IDABOUT,216,132,50,14
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO 
BEGIN
    "COOLEDIT", DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 305
        TOPMARGIN, 7
        BOTTOMMARGIN, 154
    END
END
#endif    // APSTUDIO_INVOKED

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

