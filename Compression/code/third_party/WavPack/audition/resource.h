//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by wavpack.rc
//
#define IDABOUT                         3
#define IDC_MODE_GROUP                  1000
#define IDC_LOSSLESS                    1001
#define IDC_LOSSLESS_HIGH               1002
#define IDC_LOSSLESS_VHIGH              1003
#define IDC_LOSSLESS_FAST               1004
#define IDC_HYBRID                      1005
#define IDC_HYBRID_HIGH                 1006
#define IDC_HYBRID_VHIGH                1007
#define IDC_HYBRID_FAST                 1008
#define IDC_HYBRID_GROUP                1009
#define IDC_BITRATE                     1010
#define IDC_BITRATE_TEXT                1011
#define IDC_CORRECTION                  1012
#define IDC_FLOAT_GROUP                 1013
#define IDC_FLOAT20                     1014
#define IDC_FLOAT24                     1015
#define IDC_FLOAT32                     1016
#define IDC_NORMALIZE                   1017
#define IDC_NOISESHAPE                  1023
#define IDC_DITHER                      1024
#define IDC_EXTRA_GROUP                 1025
#define IDC_EXTRA_SLIDER                1027

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        105
#define _APS_NEXT_COMMAND_VALUE         40002
#define _APS_NEXT_CONTROL_VALUE         1028
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
