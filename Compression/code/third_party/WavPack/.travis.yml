language: c
sudo: false
dist: focal
compiler:
  - clang
  - gcc
os:
  - linux
  - osx
osx_image: xcode12
addons:
  apt:
    packages:
      - xsltproc
      - docbook-xml
      - docbook-xsl
      - libxml2-utils
env:
  matrix:
  - BUILD_SYSTEM="cmake"
  - BUILD_SYSTEM="autotools"
matrix:
  exclude:
    - os: osx
      compiler: gcc
before_install:
  - if [[ "${TRAVIS_OS_NAME}" == "osx" ]]; then brew update && brew install docbook docbook-xsl libxml2 openssl; fi
script:
  - if [[ "${BUILD_SYSTEM}" == "cmake" ]]; then mkdir build && cd build && cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=ON -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON && cmake --build . && cpack -G ZIP && ctest -V; fi
  - if [[ "${BUILD_SYSTEM}" == "autotools" && "${TRAVIS_OS_NAME}" == "linux" && "${CC_FOR_BUILD}" == "clang" ]]; then ./build-regressors.sh && ./run-regressors.sh; fi
  - if [[ "${BUILD_SYSTEM}" == "autotools" && ( "${TRAVIS_OS_NAME}" != "linux" || "${CC_FOR_BUILD}" != "clang" ) ]]; then ./autogen.sh && make distcheck; fi
