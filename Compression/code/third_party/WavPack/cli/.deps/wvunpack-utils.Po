cli/wvunpack-utils.o: cli/utils.c /usr/include/stdc-predef.h \
 /usr/include/glob.h /usr/include/sys/cdefs.h /usr/include/features.h \
 /usr/include/gnu/stubs.h /usr/include/gnu/stubs-64.h \
 /usr/include/bits/wordsize.h /usr/include/bits/long-double.h \
 /usr/include/unistd.h /usr/include/bits/posix_opt.h \
 /usr/include/bits/environments.h /usr/include/bits/types.h \
 /usr/include/bits/typesizes.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stddef.h \
 /usr/include/bits/confname.h /usr/include/bits/getopt_posix.h \
 /usr/include/bits/getopt_core.h /usr/include/sys/stat.h \
 /usr/include/bits/types/struct_timespec.h \
 /usr/include/bits/types/time_t.h /usr/include/bits/stat.h \
 /usr/include/stdlib.h /usr/include/bits/libc-header-start.h \
 /usr/include/bits/waitflags.h /usr/include/bits/waitstatus.h \
 /usr/include/bits/floatn.h /usr/include/bits/floatn-common.h \
 /usr/include/sys/types.h /usr/include/bits/types/clock_t.h \
 /usr/include/bits/types/clockid_t.h /usr/include/bits/types/timer_t.h \
 /usr/include/bits/stdint-intn.h /usr/include/endian.h \
 /usr/include/bits/endian.h /usr/include/bits/byteswap.h \
 /usr/include/bits/uintn-identity.h /usr/include/sys/select.h \
 /usr/include/bits/select.h /usr/include/bits/types/sigset_t.h \
 /usr/include/bits/types/__sigset_t.h \
 /usr/include/bits/types/struct_timeval.h \
 /usr/include/bits/pthreadtypes.h /usr/include/bits/thread-shared-types.h \
 /usr/include/bits/pthreadtypes-arch.h /usr/include/alloca.h \
 /usr/include/bits/stdlib-float.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdarg.h \
 /usr/include/string.h /usr/include/bits/types/locale_t.h \
 /usr/include/bits/types/__locale_t.h /usr/include/strings.h \
 /usr/include/stdio.h /usr/include/bits/types/__fpos_t.h \
 /usr/include/bits/types/__mbstate_t.h \
 /usr/include/bits/types/__fpos64_t.h /usr/include/bits/types/__FILE.h \
 /usr/include/bits/types/FILE.h /usr/include/bits/types/struct_FILE.h \
 /usr/include/bits/stdio_lim.h /usr/include/bits/sys_errlist.h \
 /usr/include/ctype.h include/wavpack.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdint.h \
 /usr/include/stdint.h /usr/include/bits/wchar.h \
 /usr/include/bits/stdint-uintn.h cli/utils.h /usr/include/sys/time.h \
 /usr/include/signal.h /usr/include/bits/signum.h \
 /usr/include/bits/signum-generic.h \
 /usr/include/bits/types/sig_atomic_t.h \
 /usr/include/bits/types/siginfo_t.h /usr/include/bits/types/__sigval_t.h \
 /usr/include/bits/siginfo-arch.h /usr/include/bits/siginfo-consts.h \
 /usr/include/bits/types/sigval_t.h /usr/include/bits/types/sigevent_t.h \
 /usr/include/bits/sigevent-consts.h /usr/include/bits/sigaction.h \
 /usr/include/bits/sigcontext.h /usr/include/bits/types/stack_t.h \
 /usr/include/sys/ucontext.h /usr/include/bits/sigstack.h \
 /usr/include/bits/ss_flags.h /usr/include/bits/types/struct_sigstack.h \
 /usr/include/bits/sigthread.h

/usr/include/stdc-predef.h:

/usr/include/glob.h:

/usr/include/sys/cdefs.h:

/usr/include/features.h:

/usr/include/gnu/stubs.h:

/usr/include/gnu/stubs-64.h:

/usr/include/bits/wordsize.h:

/usr/include/bits/long-double.h:

/usr/include/unistd.h:

/usr/include/bits/posix_opt.h:

/usr/include/bits/environments.h:

/usr/include/bits/types.h:

/usr/include/bits/typesizes.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stddef.h:

/usr/include/bits/confname.h:

/usr/include/bits/getopt_posix.h:

/usr/include/bits/getopt_core.h:

/usr/include/sys/stat.h:

/usr/include/bits/types/struct_timespec.h:

/usr/include/bits/types/time_t.h:

/usr/include/bits/stat.h:

/usr/include/stdlib.h:

/usr/include/bits/libc-header-start.h:

/usr/include/bits/waitflags.h:

/usr/include/bits/waitstatus.h:

/usr/include/bits/floatn.h:

/usr/include/bits/floatn-common.h:

/usr/include/sys/types.h:

/usr/include/bits/types/clock_t.h:

/usr/include/bits/types/clockid_t.h:

/usr/include/bits/types/timer_t.h:

/usr/include/bits/stdint-intn.h:

/usr/include/endian.h:

/usr/include/bits/endian.h:

/usr/include/bits/byteswap.h:

/usr/include/bits/uintn-identity.h:

/usr/include/sys/select.h:

/usr/include/bits/select.h:

/usr/include/bits/types/sigset_t.h:

/usr/include/bits/types/__sigset_t.h:

/usr/include/bits/types/struct_timeval.h:

/usr/include/bits/pthreadtypes.h:

/usr/include/bits/thread-shared-types.h:

/usr/include/bits/pthreadtypes-arch.h:

/usr/include/alloca.h:

/usr/include/bits/stdlib-float.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdarg.h:

/usr/include/string.h:

/usr/include/bits/types/locale_t.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/strings.h:

/usr/include/stdio.h:

/usr/include/bits/types/__fpos_t.h:

/usr/include/bits/types/__mbstate_t.h:

/usr/include/bits/types/__fpos64_t.h:

/usr/include/bits/types/__FILE.h:

/usr/include/bits/types/FILE.h:

/usr/include/bits/types/struct_FILE.h:

/usr/include/bits/stdio_lim.h:

/usr/include/bits/sys_errlist.h:

/usr/include/ctype.h:

include/wavpack.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stdint.h:

/usr/include/stdint.h:

/usr/include/bits/wchar.h:

/usr/include/bits/stdint-uintn.h:

cli/utils.h:

/usr/include/sys/time.h:

/usr/include/signal.h:

/usr/include/bits/signum.h:

/usr/include/bits/signum-generic.h:

/usr/include/bits/types/sig_atomic_t.h:

/usr/include/bits/types/siginfo_t.h:

/usr/include/bits/types/__sigval_t.h:

/usr/include/bits/siginfo-arch.h:

/usr/include/bits/siginfo-consts.h:

/usr/include/bits/types/sigval_t.h:

/usr/include/bits/types/sigevent_t.h:

/usr/include/bits/sigevent-consts.h:

/usr/include/bits/sigaction.h:

/usr/include/bits/sigcontext.h:

/usr/include/bits/types/stack_t.h:

/usr/include/sys/ucontext.h:

/usr/include/bits/sigstack.h:

/usr/include/bits/ss_flags.h:

/usr/include/bits/types/struct_sigstack.h:

/usr/include/bits/sigthread.h:
