cli/wvunpack-md5.o: cli/md5.c /usr/include/stdc-predef.h \
 /usr/include/string.h /usr/include/bits/libc-header-start.h \
 /usr/include/features.h /usr/include/sys/cdefs.h \
 /usr/include/bits/wordsize.h /usr/include/bits/long-double.h \
 /usr/include/gnu/stubs.h /usr/include/gnu/stubs-64.h \
 /usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stddef.h \
 /usr/include/bits/types/locale_t.h /usr/include/bits/types/__locale_t.h \
 /usr/include/strings.h cli/md5.h

/usr/include/stdc-predef.h:

/usr/include/string.h:

/usr/include/bits/libc-header-start.h:

/usr/include/features.h:

/usr/include/sys/cdefs.h:

/usr/include/bits/wordsize.h:

/usr/include/bits/long-double.h:

/usr/include/gnu/stubs.h:

/usr/include/gnu/stubs-64.h:

/usr/lib/gcc/x86_64-linux-gnu/7.3.0/include/stddef.h:

/usr/include/bits/types/locale_t.h:

/usr/include/bits/types/__locale_t.h:

/usr/include/strings.h:

cli/md5.h:
