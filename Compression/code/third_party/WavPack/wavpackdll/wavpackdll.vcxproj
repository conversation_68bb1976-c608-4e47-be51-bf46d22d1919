<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1A87F412-BA74-4DBB-9F77-FD55C042FB63}</ProjectGuid>
    <RootNamespace>wavpackdll</RootNamespace>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>16.0.30804.86</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Link>
      <AdditionalOptions>/export:WavpackOpenFileInput /export:WavpackOpenFileInputEx /export:WavpackGetMode
/export:WavpackGetVersion /export:WavpackGetErrorMessage /export:WavpackUnpackSamples
/export:WavpackSeekSample /export:WavpackGetNumTagItems /export:WavpackGetNumBinaryTagItems /export:WavpackGetTagItem /export:WavpackGetBinaryTagItem
/export:WavpackGetEncodedNoise

/export:WavpackGetTagItemIndexed /export:WavpackGetBinaryTagItemIndexed /export:WavpackOpenFileOutput
/export:WavpackSetConfiguration /export:WavpackPackInit /export:WavpackPackSamples
/export:WavpackFlushSamples /export:WavpackAddWrapper /export:WavpackStoreMD5Sum
/export:WavpackUpdateNumSamples /export:WavpackGetWrapperLocation
/export:WavpackAppendTagItem /export:WavpackAppendBinaryTagItem /export:WavpackDeleteTagItem /export:WavpackWriteTag
/export:WavpackGetNumSamples /export:WavpackGetSampleIndex /export:WavpackGetNumErrors
/export:WavpackLossyBlocks /export:WavpackGetProgress /export:WavpackGetFileSize
/export:WavpackGetRatio /export:WavpackGetAverageBitrate /export:WavpackGetInstantBitrate
/export:WavpackCloseFile /export:WavpackGetSampleRate /export:WavpackGetNumChannels
/export:WavpackGetChannelMask /export:WavpackGetFloatNormExp
/export:WavpackGetBitsPerSample /export:WavpackGetBytesPerSample
/export:WavpackGetReducedChannels /export:WavpackGetWrapperBytes
/export:WavpackGetWrapperData /export:WavpackFreeWrapper
/export:WavpackSeekTrailingWrapper /export:WavpackGetMD5Sum
/export:WavpackBigEndianToNative /export:WavpackNativeToBigEndian
/export:WavpackLittleEndianToNative /export:WavpackNativeToLittleEndian
/export:WavpackGetLibraryVersion /export:WavpackGetLibraryVersionString

/export:WavpackOpenRawDecoder /export:WavpackOpenFileInputEx64
/export:WavpackGetNumSamples64 /export:WavpackGetSampleIndex64
/export:WavpackSeekSample64 /export:WavpackGetFileSize64
/export:WavpackGetQualifyMode /export:WavpackGetFileExtension
/export:WavpackGetFileFormat /export:WavpackGetNumSamplesInFrame
/export:WavpackGetNativeSampleRate /export:WavpackGetChannelIdentities
/export:WavpackGetChannelLayout /export:WavpackSetFileInformation
/export:WavpackSetConfiguration64 /export:WavpackSetChannelLayout
/export:WavpackVerifySingleBlock
/export:WavpackFloatNormalize
 %(AdditionalOptions)</AdditionalOptions>
      <OutputFile>$(OutDir)wavpackdll.dll</OutputFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Link>
      <AdditionalOptions>/export:WavpackOpenFileInput /export:WavpackOpenFileInputEx /export:WavpackGetMode
/export:WavpackGetVersion /export:WavpackGetErrorMessage /export:WavpackUnpackSamples
/export:WavpackSeekSample /export:WavpackGetNumTagItems /export:WavpackGetNumBinaryTagItems /export:WavpackGetTagItem /export:WavpackGetBinaryTagItem
/export:WavpackGetEncodedNoise

/export:WavpackGetTagItemIndexed /export:WavpackGetBinaryTagItemIndexed /export:WavpackOpenFileOutput
/export:WavpackSetConfiguration /export:WavpackPackInit /export:WavpackPackSamples
/export:WavpackFlushSamples /export:WavpackAddWrapper /export:WavpackStoreMD5Sum
/export:WavpackUpdateNumSamples /export:WavpackGetWrapperLocation
/export:WavpackAppendTagItem /export:WavpackAppendBinaryTagItem /export:WavpackDeleteTagItem /export:WavpackWriteTag
/export:WavpackGetNumSamples /export:WavpackGetSampleIndex /export:WavpackGetNumErrors
/export:WavpackLossyBlocks /export:WavpackGetProgress /export:WavpackGetFileSize
/export:WavpackGetRatio /export:WavpackGetAverageBitrate /export:WavpackGetInstantBitrate
/export:WavpackCloseFile /export:WavpackGetSampleRate /export:WavpackGetNumChannels
/export:WavpackGetChannelMask /export:WavpackGetFloatNormExp
/export:WavpackGetBitsPerSample /export:WavpackGetBytesPerSample
/export:WavpackGetReducedChannels /export:WavpackGetWrapperBytes
/export:WavpackGetWrapperData /export:WavpackFreeWrapper
/export:WavpackSeekTrailingWrapper /export:WavpackGetMD5Sum
/export:WavpackBigEndianToNative /export:WavpackNativeToBigEndian
/export:WavpackLittleEndianToNative /export:WavpackNativeToLittleEndian
/export:WavpackGetLibraryVersion /export:WavpackGetLibraryVersionString

/export:WavpackOpenRawDecoder /export:WavpackOpenFileInputEx64
/export:WavpackGetNumSamples64 /export:WavpackGetSampleIndex64
/export:WavpackSeekSample64 /export:WavpackGetFileSize64
/export:WavpackGetQualifyMode /export:WavpackGetFileExtension
/export:WavpackGetFileFormat /export:WavpackGetNumSamplesInFrame
/export:WavpackGetNativeSampleRate /export:WavpackGetChannelIdentities
/export:WavpackGetChannelLayout /export:WavpackSetFileInformation
/export:WavpackSetConfiguration64 /export:WavpackSetChannelLayout
/export:WavpackVerifySingleBlock
/export:WavpackFloatNormalize
 %(AdditionalOptions)</AdditionalOptions>
      <OutputFile>$(OutDir)wavpackdll.dll</OutputFile>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <AdditionalIncludeDirectories>..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Link>
      <AdditionalOptions>/export:WavpackOpenFileInput /export:WavpackOpenFileInputEx /export:WavpackGetMode
/export:WavpackGetVersion /export:WavpackGetErrorMessage /export:WavpackUnpackSamples
/export:WavpackSeekSample /export:WavpackGetNumTagItems /export:WavpackGetNumBinaryTagItems /export:WavpackGetTagItem /export:WavpackGetBinaryTagItem
/export:WavpackGetEncodedNoise

/export:WavpackGetTagItemIndexed /export:WavpackGetBinaryTagItemIndexed /export:WavpackOpenFileOutput
/export:WavpackSetConfiguration /export:WavpackPackInit /export:WavpackPackSamples
/export:WavpackFlushSamples /export:WavpackAddWrapper /export:WavpackStoreMD5Sum
/export:WavpackUpdateNumSamples /export:WavpackGetWrapperLocation
/export:WavpackAppendTagItem /export:WavpackAppendBinaryTagItem /export:WavpackDeleteTagItem /export:WavpackWriteTag
/export:WavpackGetNumSamples /export:WavpackGetSampleIndex /export:WavpackGetNumErrors
/export:WavpackLossyBlocks /export:WavpackGetProgress /export:WavpackGetFileSize
/export:WavpackGetRatio /export:WavpackGetAverageBitrate /export:WavpackGetInstantBitrate
/export:WavpackCloseFile /export:WavpackGetSampleRate /export:WavpackGetNumChannels
/export:WavpackGetChannelMask /export:WavpackGetFloatNormExp
/export:WavpackGetBitsPerSample /export:WavpackGetBytesPerSample
/export:WavpackGetReducedChannels /export:WavpackGetWrapperBytes
/export:WavpackGetWrapperData /export:WavpackFreeWrapper
/export:WavpackSeekTrailingWrapper /export:WavpackGetMD5Sum
/export:WavpackBigEndianToNative /export:WavpackNativeToBigEndian
/export:WavpackLittleEndianToNative /export:WavpackNativeToLittleEndian
/export:WavpackGetLibraryVersion /export:WavpackGetLibraryVersionString

/export:WavpackOpenRawDecoder /export:WavpackOpenFileInputEx64
/export:WavpackGetNumSamples64 /export:WavpackGetSampleIndex64
/export:WavpackSeekSample64 /export:WavpackGetFileSize64
/export:WavpackGetQualifyMode /export:WavpackGetFileExtension
/export:WavpackGetFileFormat /export:WavpackGetNumSamplesInFrame
/export:WavpackGetNativeSampleRate /export:WavpackGetChannelIdentities
/export:WavpackGetChannelLayout /export:WavpackSetFileInformation
/export:WavpackSetConfiguration64 /export:WavpackSetChannelLayout
/export:WavpackVerifySingleBlock
/export:WavpackFloatNormalize
 %(AdditionalOptions)</AdditionalOptions>
      <OutputFile>$(OutDir)wavpackdll.dll</OutputFile>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <AdditionalIncludeDirectories>..\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>Default</CompileAs>
    </ClCompile>
    <Link>
      <AdditionalOptions>/export:WavpackOpenFileInput /export:WavpackOpenFileInputEx /export:WavpackGetMode
/export:WavpackGetVersion /export:WavpackGetErrorMessage /export:WavpackUnpackSamples
/export:WavpackSeekSample /export:WavpackGetNumTagItems /export:WavpackGetNumBinaryTagItems /export:WavpackGetTagItem /export:WavpackGetBinaryTagItem
/export:WavpackGetEncodedNoise

/export:WavpackGetTagItemIndexed /export:WavpackGetBinaryTagItemIndexed /export:WavpackOpenFileOutput
/export:WavpackSetConfiguration /export:WavpackPackInit /export:WavpackPackSamples
/export:WavpackFlushSamples /export:WavpackAddWrapper /export:WavpackStoreMD5Sum
/export:WavpackUpdateNumSamples /export:WavpackGetWrapperLocation
/export:WavpackAppendTagItem /export:WavpackAppendBinaryTagItem /export:WavpackDeleteTagItem /export:WavpackWriteTag
/export:WavpackGetNumSamples /export:WavpackGetSampleIndex /export:WavpackGetNumErrors
/export:WavpackLossyBlocks /export:WavpackGetProgress /export:WavpackGetFileSize
/export:WavpackGetRatio /export:WavpackGetAverageBitrate /export:WavpackGetInstantBitrate
/export:WavpackCloseFile /export:WavpackGetSampleRate /export:WavpackGetNumChannels
/export:WavpackGetChannelMask /export:WavpackGetFloatNormExp
/export:WavpackGetBitsPerSample /export:WavpackGetBytesPerSample
/export:WavpackGetReducedChannels /export:WavpackGetWrapperBytes
/export:WavpackGetWrapperData /export:WavpackFreeWrapper
/export:WavpackSeekTrailingWrapper /export:WavpackGetMD5Sum
/export:WavpackBigEndianToNative /export:WavpackNativeToBigEndian
/export:WavpackLittleEndianToNative /export:WavpackNativeToLittleEndian
/export:WavpackGetLibraryVersion /export:WavpackGetLibraryVersionString

/export:WavpackOpenRawDecoder /export:WavpackOpenFileInputEx64
/export:WavpackGetNumSamples64 /export:WavpackGetSampleIndex64
/export:WavpackSeekSample64 /export:WavpackGetFileSize64
/export:WavpackGetQualifyMode /export:WavpackGetFileExtension
/export:WavpackGetFileFormat /export:WavpackGetNumSamplesInFrame
/export:WavpackGetNativeSampleRate /export:WavpackGetChannelIdentities
/export:WavpackGetChannelLayout /export:WavpackSetFileInformation
/export:WavpackSetConfiguration64 /export:WavpackSetChannelLayout
/export:WavpackVerifySingleBlock
/export:WavpackFloatNormalize
 %(AdditionalOptions)</AdditionalOptions>
      <OutputFile>$(OutDir)wavpackdll.dll</OutputFile>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <DataExecutionPrevention />
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="..\src\libwavpack.vcxproj">
      <Project>{5cccb9cf-0384-458f-ba08-72b73866840f}</Project>
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="wavpackdll.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dummy.c" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets" />
  </ImportGroup>
</Project>