AC_INIT
AC_CONFIG_SRCDIR([src/libwavpack.cpp])
AC_CONFIG_AUX_DIR(config)
AM_INIT_AUTOMAKE(libwavpack,1.0.3)
AC_CONFIG_HEADER(src/config.h)

VERSION="1.0.3"

AC_PROG_CXX
AM_DISABLE_STATIC
AC_PROG_LIBTOOL
AC_PROG_INSTALL

CXXFLAGS="$CXXFLAGS -O3 -fomit-frame-pointer"

AC_HEADER_STDC
AC_CHECK_HEADERS([fcntl.h stdlib.h string.h unistd.h wchar.h])

AC_HEADER_STDBOOL
AC_C_CONST
AC_TYPE_OFF_T
AC_TYPE_SIZE_T

AM_PATH_XMMS(1.2.10,,AC_MSG_ERROR([*** Xmms not installed - please install first ***]))

PKG_CHECK_MODULES(GTK, [gtk+ >= 1.2.2],
	[],
	[AC_MSG_ERROR([GTK+ >= 1.2.2 development package not installed])]
)

PKG_CHECK_MODULES(GLIB, [glib >= 1.2.10],
	[],
	[AC_MSG_ERROR([GLib+ >= 1.2.10 development package not installed])]
)

AC_CHECK_HEADERS([wavpack/wavpack.h],,AC_MSG_ERROR([*** Libwavpack not installed - please install first ***]))
AC_CHECK_LIB(wavpack, WavpackGetVersion,[],AC_MSG_ERROR([*** libwavpack not found]))

AC_CHECK_HEADERS([iconv.h],,AC_MSG_ERROR([*** Can't find iconv.h header]))

dnl pthread libs

AC_CHECK_HEADER(pthread.h,,AC_MSG_ERROR([*** POSIX thread support not installed - please install first ***]))

PTHREAD_LIBS=error
AC_MSG_CHECKING(for old style FreeBSD -pthread flag)
AC_EGREP_CPP(yes,
	[#if (defined(__FreeBSD_cc_version) && __FreeBSD_cc_version <= 500001) || defined(__OpenBSD__)
	  yes
	#endif
	], AC_MSG_RESULT(yes)
	CFLAGS="$CFLAGS -D_THREAD_SAFE" PTHREAD_LIBS="-pthread",
	AC_MSG_RESULT(no))
if test "x$PTHREAD_LIBS" = xerror; then
	AC_CHECK_LIB(pthread, pthread_attr_init,
		PTHREAD_LIBS="-lpthread")
fi
if test "x$PTHREAD_LIBS" = xerror; then
	AC_CHECK_LIB(pthreads, pthread_attr_init,
     		PTHREAD_LIBS="-lpthreads")
fi
if test "x$PTHREAD_LIBS" = xerror; then
	AC_CHECK_LIB(c_r, pthread_attr_init,
     		PTHREAD_LIBS="-lc_r")
fi
if test "x$PTHREAD_LIBS" = xerror; then
	AC_CHECK_FUNC(pthread_attr_init, PTHREAD_LIBS="")
fi
if test "x$PTHREAD_LIBS" = xerror; then
	AC_MSG_ERROR(*** Unable to locate working posix thread library ***)
fi
AC_SUBST(PTHREAD_LIBS)

dnl

AC_SUBST(VERSION)
AC_CONFIG_FILES([Makefile src/Makefile])
AC_OUTPUT
AC_MSG_RESULT([ xmms-wavpack $VERSION configured successfully.])
