﻿<?xml version="1.0" encoding="utf-8"?>
<VisualStudioToolFile
	Name="Microsoft Macro Assembler"
	Version="8.00"
	>
	<Rules>
		<CustomBuildRule
			Name="MASM"
			DisplayName="Microsoft Macro Assembler"
			CommandLine="ml.exe /c [AllOptions] [AdditionalOptions] /Ta[inputs]"
			Outputs="[$ObjectFileName]"
			FileExtensions="*.asm"
			ExecutionDescription="Assembling..."
			>
			<Properties>
				<BooleanProperty
					Name="NoLogo"
					DisplayName="Suppress Startup Banner"
					Description="Suppress the display of the startup banner and information messages.     (/nologo)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/nologo"
					DefaultValue="true"
				/>
				<BooleanProperty
					Name="TinyMemoryModelSupport"
					DisplayName="Tiny Memory Model Support"
					PropertyPageName="Advanced"
					Description="Enables tiny-memory-model support. Note that this is not equivalent to the .MODEL TINY directive.     (/AT)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/AT"
				/>
				<StringProperty
					Name="ObjectFileName"
					DisplayName="Object File Name"
					PropertyPageName="Object File"
					Description="Specifies the name of the output object file.     (/Fo:[file])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Fo&quot;[value]&quot;"
					DefaultValue="$(IntDir)\$(InputName).obj"
				/>
				<EnumProperty
					Name="PreserveIdentifierCase"
					DisplayName="Preserve Identifier Case"
					Description="Specifies preservation of case of user identifiers.     (/Cp, /Cu, /Cx)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					DefaultValue="0"
					>
					<Values>
						<EnumValue
							Value="0"
							DisplayName="Default"
						/>
						<EnumValue
							Value="1"
							Switch="/Cp"
							DisplayName="Preserves Identifier Case (/Cp)"
						/>
						<EnumValue
							Value="2"
							Switch="/Cu"
							DisplayName="Maps all identifiers to upper case. (/Cu)"
						/>
						<EnumValue
							Value="3"
							Switch="/Cx"
							DisplayName="Preserves case in public and extern symbols. (/Cx)"
						/>
					</Values>
				</EnumProperty>
				<StringProperty
					Name="PreprocessorDefinitions"
					DisplayName="Preprocessor Definitions"
					Description="Defines a text macro with the given name.     (/D[symbol])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/D&quot;[value]&quot;"
					Delimited="true"
					Inheritable="true"
				/>
				<BooleanProperty
					Name="GeneratePreprocessedSourceListing"
					DisplayName="Generate Preprocessed Source Listing"
					PropertyPageName="Listing File"
					Description="Generates a preprocessed source listing to the Output Window.     (/EP)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/EP"
				/>
				<StringProperty
					Name="AssembledCodeListingFile"
					DisplayName="Assembled Code Listing File"
					PropertyPageName="Listing File"
					Description="Generates an assembled code listing file.     (/Fl[file])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Fl&quot;[value]&quot;"
				/>
				<StringProperty
					Name="SourceListingLineWidth"
					DisplayName="Source Listing Line Width"
					PropertyPageName="Listing File"
					Description="Sets the line width of source listing in characters per line. Range is 60 to 255. Same as PAGE width.     (/Sl [width])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sl [value]"
				/>
				<StringProperty
					Name="SourceListingPageLength"
					DisplayName="Source Listing Page Length"
					PropertyPageName="Listing File"
					Description="Sets the page length of source listing in lines per page. Range is 10 to 255. Same as PAGE length.     (/Sp [length])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sp [value]"
				/>
				<StringProperty
					Name="IncludePaths"
					DisplayName="Include Paths"
					Description="Sets path for include file. A maximum of 10 /I options is allowed.     (/I [path])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/I &quot;[value]&quot;"
					Delimited="true"
					Inheritable="true"
				/>
				<BooleanProperty
					Name="ListAllAvailableInformation"
					DisplayName="List All Available Information"
					PropertyPageName="Listing File"
					Description="Turns on listing of all available information.     (/Sa)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sa"
				/>
				<BooleanProperty
					Name="UseSafeExceptionHandlers"
					DisplayName="Use Safe Exception Handlers"
					PropertyPageName="Advanced"
					Description="Marks the object as either containing no exception handlers or containing exception handlers that are all declared with .SAFESEH.     (/safeseh)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/safeseh"
				/>
				<BooleanProperty
					Name="AddFirstPassListing"
					DisplayName="Add First Pass Listing"
					PropertyPageName="Listing File"
					Description="Adds first-pass listing to listing file.     (/Sf)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sf"
				/>
				<BooleanProperty
					Name="EnableAssemblyGeneratedCodeListing"
					DisplayName="Enable Assembly Generated Code Listing"
					PropertyPageName="Listing File"
					Description="Turns on listing of assembly-generated code.     (/Sg)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sg"
				/>
				<BooleanProperty
					Name="DisableSymbolTable"
					DisplayName="Disable Symbol Table"
					PropertyPageName="Listing File"
					Description="Turns off symbol table when producing a listing.     (/Sn)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sn"
				/>
				<StringProperty
					Name="SourceListingSubTitle"
					DisplayName="Source Listing Subtitle"
					PropertyPageName="Listing File"
					Description="Specifies subtitle text for source listing. Same as SUBTITLE text.     (/Ss [subtitle])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Ss [value]"
				/>
				<StringProperty
					Name="SourceListingTitle"
					DisplayName="Source Listing Title"
					PropertyPageName="Listing File"
					Description="Specifies title for source listing. Same as TITLE text.     (/St [title])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/St [value]"
				/>
				<BooleanProperty
					Name="EnableFalseConditionalsInListing"
					DisplayName="Enable False Conditionals In Listing"
					PropertyPageName="Listing File"
					Description="Turns on false conditionals in listing.     (/Sx)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sx"
				/>
				<EnumProperty
					Name="WarningLevel"
					DisplayName="Warning Level"
					Description="Sets the warning level, where level = 0, 1, 2, or 3.    (/W0, /W1, /W2, /W3)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					DefaultValue="3"
					>
					<Values>
						<EnumValue
							Value="0"
							Switch="/W0"
							DisplayName="Warning Level 0 (/W0)"
						/>
						<EnumValue
							Value="1"
							Switch="/W1"
							DisplayName="Warning Level 1 (/W1)"
						/>
						<EnumValue
							Value="2"
							Switch="/W2"
							DisplayName="Warning Level 2 (/W2)"
						/>
						<EnumValue
							Value="3"
							Switch="/W3"
							DisplayName="Warning Level 3 (/W3)"
						/>
					</Values>
				</EnumProperty>
				<BooleanProperty
					Name="TreatWarningsAsErrors"
					DisplayName="Treat Warnings As Errors"
					Description="Returns an error code if warnings are generated.     (/WX)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/WX"
				/>
				<BooleanProperty
					Name="MakeAllSymbolsPublic"
					DisplayName="Make All Symbols Public"
					PropertyPageName="Object File"
					Description="Makes all symbols public.     (/Zf)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zf"
				/>
				<BooleanProperty
					Name="GenerateDebugInformation"
					DisplayName="Generate Debug Information"
					Description="Generates Debug Information.     (/Zi)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zi"
					DefaultValue="true"
				/>
				<BooleanProperty
					Name="EnableMASM51Compatibility"
					DisplayName="Enable MASM 5.1 Compatibility"
					Description="Enables M510 option for maximum compatibility with MASM 5.1.     (/Zm)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zm"
				/>
				<EnumProperty
					Name="PackAlignmentBoundary"
					DisplayName="Pack Alignment Boundary"
					PropertyPageName="Advanced"
					Description="Packs structures on the specified byte boundary. The alignment can be 1, 2, 4, 8 or 16.     (/Zp1, /Zp2, /Zp4, /Zp8, /Zp16)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					>
					<Values>
						<EnumValue
							Value="0"
							DisplayName="Default"
						/>
						<EnumValue
							Value="1"
							Switch="/Zp1"
							DisplayName="One Byte Boundary (/Zp1)"
						/>
						<EnumValue
							Value="2"
							Switch="/Zp2"
							DisplayName="Two Byte Boundary (/Zp2)"
						/>
						<EnumValue
							Value="3"
							Switch="/Zp4"
							DisplayName="Four Byte Boundary (/Zp4)"
						/>
						<EnumValue
							Value="4"
							Switch="/Zp8"
							DisplayName="Eight Byte Boundary (/Zp8)"
						/>
						<EnumValue
							Value="5"
							Switch="/Zp16"
							DisplayName="Sixteen Byte Boundary (/Zp16)"
						/>
					</Values>
				</EnumProperty>
				<BooleanProperty
					Name="PerformSyntaxCheckOnly"
					DisplayName="Perform Syntax Check Only"
					Description="Performs a syntax check only.     (/Zs)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zs"
				/>
				<EnumProperty
					Name="CallingConvention"
					DisplayName="Calling Convention"
					PropertyPageName="Advanced"
					Description="Selects calling convention for your application.     (/Gc, /Gd. /Gz)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					>
					<Values>
						<EnumValue
							Value="0"
							DisplayName="Default"
						/>
						<EnumValue
							Value="1"
							Switch="/Gd"
							DisplayName="Use C-style Calling Convention (/Gd)"
						/>
						<EnumValue
							Value="2"
							Switch="/Gz"
							DisplayName="Use stdcall Calling Convention (/Gz)"
						/>
						<EnumValue
							Value="3"
							Switch="/Gc"
							DisplayName="Use Pascal Calling Convention (/Gc)"
						/>
					</Values>
				</EnumProperty>
				<EnumProperty
					Name="ErrorReporting"
					DisplayName="Error Reporting"
					PropertyPageName="Advanced"
					Description="Reports internal assembler errors to Microsoft.     (/errorReport:[method])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					>
					<Values>
						<EnumValue
							Value="0"
							Switch="/errorReport:prompt"
							DisplayName="Prompt to send report immediately (/errorReport:prompt)"
						/>
						<EnumValue
							Value="1"
							Switch="/errorReport:queue"
							DisplayName="Prompt to send report at the next logon (/errorReport:queue)"
						/>
						<EnumValue
							Value="2"
							Switch="/errorReport:send"
							DisplayName="Automatically send report (/errorReport:send)"
						/>
						<EnumValue
							Value="3"
							Switch="/errorReport:none"
							DisplayName="Do not send report (/errorReport:none)"
						/>
					</Values>
				</EnumProperty>
				<StringProperty
					Name="BrowseFile"
					DisplayName="Generate Browse Information File"
					PropertyPageName="Advanced"
					Description="Specifies whether to generate browse information file and its optional name or location of the browse information file.     (/FR[name])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/FR&quot;[value]&quot;"
					Delimited="true"
					Inheritable="true"
				/>
			</Properties>
		</CustomBuildRule>
		<CustomBuildRule
			Name="MASM64"
			DisplayName="Microsoft Macro Assembler for x64"
			CommandLine="ml64.exe /c [AllOptions] [AdditionalOptions] /Ta[inputs]"
			Outputs="[$ObjectFileName]"
			FileExtensions="*.asm"
			ExecutionDescription="Assembling..."
			>
			<Properties>
				<BooleanProperty
					Name="NoLogo"
					DisplayName="Suppress Startup Banner"
					Description="Suppress the display of the startup banner and information messages.     (/nologo)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/nologo"
					DefaultValue="true"
				/>
				<BooleanProperty
					Name="TinyMemoryModelSupport"
					DisplayName="Tiny Memory Model Support"
					PropertyPageName="Advanced"
					Description="Enables tiny-memory-model support. Note that this is not equivalent to the .MODEL TINY directive.     (/AT)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/AT"
				/>
				<StringProperty
					Name="ObjectFileName"
					DisplayName="Object File Name"
					PropertyPageName="Object File"
					Description="Specifies the name of the output object file.     (/Fo:[file])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Fo&quot;[value]&quot;"
					DefaultValue="$(IntDir)\$(InputName).obj"
				/>
				<EnumProperty
					Name="PreserveIdentifierCase"
					DisplayName="Preserve Identifier Case"
					Description="Specifies preservation of case of user identifiers.     (/Cp, /Cu, /Cx)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					DefaultValue="0"
					>
					<Values>
						<EnumValue
							Value="0"
							DisplayName="Default"
						/>
						<EnumValue
							Value="1"
							Switch="/Cp"
							DisplayName="Preserves Identifier Case (/Cp)"
						/>
						<EnumValue
							Value="2"
							Switch="/Cu"
							DisplayName="Maps all identifiers to upper case. (/Cu)"
						/>
						<EnumValue
							Value="3"
							Switch="/Cx"
							DisplayName="Preserves case in public and extern symbols. (/Cx)"
						/>
					</Values>
				</EnumProperty>
				<StringProperty
					Name="PreprocessorDefinitions"
					DisplayName="Preprocessor Definitions"
					Description="Defines a text macro with the given name.     (/D[symbol])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/D&quot;[value]&quot;"
					Delimited="true"
					Inheritable="true"
				/>
				<BooleanProperty
					Name="GeneratePreprocessedSourceListing"
					DisplayName="Generate Preprocessed Source Listing"
					PropertyPageName="Listing File"
					Description="Generates a preprocessed source listing to the Output Window.     (/EP)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/EP"
				/>
				<StringProperty
					Name="AssembledCodeListingFile"
					DisplayName="Assembled Code Listing File"
					PropertyPageName="Listing File"
					Description="Generates an assembled code listing file.     (/Fl[file])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Fl&quot;[value]&quot;"
				/>
				<StringProperty
					Name="SourceListingLineWidth"
					DisplayName="Source Listing Line Width"
					PropertyPageName="Listing File"
					Description="Sets the line width of source listing in characters per line. Range is 60 to 255. Same as PAGE width.     (/Sl [width])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sl [value]"
				/>
				<StringProperty
					Name="SourceListingPageLength"
					DisplayName="Source Listing Page Length"
					PropertyPageName="Listing File"
					Description="Sets the page length of source listing in lines per page. Range is 10 to 255. Same as PAGE length.     (/Sp [length])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sp [value]"
				/>
				<StringProperty
					Name="IncludePaths"
					DisplayName="Include Paths"
					Description="Sets path for include file. A maximum of 10 /I options is allowed.     (/I [path])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/I &quot;[value]&quot;"
					Delimited="true"
					Inheritable="true"
				/>
				<BooleanProperty
					Name="ListAllAvailableInformation"
					DisplayName="List All Available Information"
					PropertyPageName="Listing File"
					Description="Turns on listing of all available information.     (/Sa)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sa"
				/>
				<BooleanProperty
					Name="UseSafeExceptionHandlers"
					DisplayName="Use Safe Exception Handlers"
					PropertyPageName="Advanced"
					Description="Marks the object as either containing no exception handlers or containing exception handlers that are all declared with .SAFESEH.     (/safeseh)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/safeseh"
				/>
				<BooleanProperty
					Name="AddFirstPassListing"
					DisplayName="Add First Pass Listing"
					PropertyPageName="Listing File"
					Description="Adds first-pass listing to listing file.     (/Sf)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sf"
				/>
				<BooleanProperty
					Name="EnableAssemblyGeneratedCodeListing"
					DisplayName="Enable Assembly Generated Code Listing"
					PropertyPageName="Listing File"
					Description="Turns on listing of assembly-generated code.     (/Sg)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sg"
				/>
				<BooleanProperty
					Name="DisableSymbolTable"
					DisplayName="Disable Symbol Table"
					PropertyPageName="Listing File"
					Description="Turns off symbol table when producing a listing.     (/Sn)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sn"
				/>
				<StringProperty
					Name="SourceListingSubTitle"
					DisplayName="Source Listing Subtitle"
					PropertyPageName="Listing File"
					Description="Specifies subtitle text for source listing. Same as SUBTITLE text.     (/Ss [subtitle])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Ss [value]"
				/>
				<StringProperty
					Name="SourceListingTitle"
					DisplayName="Source Listing Title"
					PropertyPageName="Listing File"
					Description="Specifies title for source listing. Same as TITLE text.     (/St [title])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/St [value]"
				/>
				<BooleanProperty
					Name="EnableFalseConditionalsInListing"
					DisplayName="Enable False Conditionals In Listing"
					PropertyPageName="Listing File"
					Description="Turns on false conditionals in listing.     (/Sx)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Sx"
				/>
				<EnumProperty
					Name="WarningLevel"
					DisplayName="Warning Level"
					Description="Sets the warning level, where level = 0, 1, 2, or 3.    (/W0, /W1, /W2, /W3)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					DefaultValue="3"
					>
					<Values>
						<EnumValue
							Value="0"
							Switch="/W0"
							DisplayName="Warning Level 0 (/W0)"
						/>
						<EnumValue
							Value="1"
							Switch="/W1"
							DisplayName="Warning Level 1 (/W1)"
						/>
						<EnumValue
							Value="2"
							Switch="/W2"
							DisplayName="Warning Level 2 (/W2)"
						/>
						<EnumValue
							Value="3"
							Switch="/W3"
							DisplayName="Warning Level 3 (/W3)"
						/>
					</Values>
				</EnumProperty>
				<BooleanProperty
					Name="TreatWarningsAsErrors"
					DisplayName="Treat Warnings As Errors"
					Description="Returns an error code if warnings are generated.     (/WX)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/WX"
				/>
				<BooleanProperty
					Name="MakeAllSymbolsPublic"
					DisplayName="Make All Symbols Public"
					PropertyPageName="Object File"
					Description="Makes all symbols public.     (/Zf)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zf"
				/>
				<BooleanProperty
					Name="GenerateDebugInformation"
					DisplayName="Generate Debug Information"
					Description="Generates Debug Information.     (/Zi)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zi"
					DefaultValue="true"
				/>
				<BooleanProperty
					Name="EnableMASM51Compatibility"
					DisplayName="Enable MASM 5.1 Compatibility"
					Description="Enables M510 option for maximum compatibility with MASM 5.1.     (/Zm)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zm"
				/>
				<EnumProperty
					Name="PackAlignmentBoundary"
					DisplayName="Pack Alignment Boundary"
					PropertyPageName="Advanced"
					Description="Packs structures on the specified byte boundary. The alignment can be 1, 2, 4, 8 or 16.     (/Zp1, /Zp2, /Zp4, /Zp8, /Zp16)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					>
					<Values>
						<EnumValue
							Value="0"
							DisplayName="Default"
						/>
						<EnumValue
							Value="1"
							Switch="/Zp1"
							DisplayName="One Byte Boundary (/Zp1)"
						/>
						<EnumValue
							Value="2"
							Switch="/Zp2"
							DisplayName="Two Byte Boundary (/Zp2)"
						/>
						<EnumValue
							Value="3"
							Switch="/Zp4"
							DisplayName="Four Byte Boundary (/Zp4)"
						/>
						<EnumValue
							Value="4"
							Switch="/Zp8"
							DisplayName="Eight Byte Boundary (/Zp8)"
						/>
						<EnumValue
							Value="5"
							Switch="/Zp16"
							DisplayName="Sixteen Byte Boundary (/Zp16)"
						/>
					</Values>
				</EnumProperty>
				<BooleanProperty
					Name="PerformSyntaxCheckOnly"
					DisplayName="Perform Syntax Check Only"
					Description="Performs a syntax check only.     (/Zs)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/Zs"
				/>
				<EnumProperty
					Name="CallingConvention"
					DisplayName="Calling Convention"
					PropertyPageName="Advanced"
					Description="Selects calling convention for your application.     (/Gc, /Gd. /Gz)"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					>
					<Values>
						<EnumValue
							Value="0"
							DisplayName="Default"
						/>
						<EnumValue
							Value="1"
							Switch="/Gd"
							DisplayName="Use C-style Calling Convention (/Gd)"
						/>
						<EnumValue
							Value="2"
							Switch="/Gz"
							DisplayName="Use stdcall Calling Convention (/Gz)"
						/>
						<EnumValue
							Value="3"
							Switch="/Gc"
							DisplayName="Use Pascal Calling Convention (/Gc)"
						/>
					</Values>
				</EnumProperty>
				<EnumProperty
					Name="ErrorReporting"
					DisplayName="Error Reporting"
					PropertyPageName="Advanced"
					Description="Reports internal assembler errors to Microsoft.     (/errorReport:[method])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					>
					<Values>
						<EnumValue
							Value="0"
							Switch="/errorReport:prompt"
							DisplayName="Prompt to send report immediately (/errorReport:prompt)"
						/>
						<EnumValue
							Value="1"
							Switch="/errorReport:queue"
							DisplayName="Prompt to send report at the next logon (/errorReport:queue)"
						/>
						<EnumValue
							Value="2"
							Switch="/errorReport:send"
							DisplayName="Automatically send report (/errorReport:send)"
						/>
						<EnumValue
							Value="3"
							Switch="/errorReport:none"
							DisplayName="Do not send report (/errorReport:none)"
						/>
					</Values>
				</EnumProperty>
				<StringProperty
					Name="BrowseFile"
					DisplayName="Generate Browse Information File"
					PropertyPageName="Advanced"
					Description="Specifies whether to generate browse information file and its optional name or location of the browse information file.     (/FR[name])"
					HelpURL="http://msdn.microsoft.com/library/default.asp?url=/library/en-us/vcmasm/html/vclrfml.asp"
					Switch="/FR&quot;[value]&quot;"
					Delimited="true"
					Inheritable="true"
				/>
			</Properties>
		</CustomBuildRule>
	</Rules>
</VisualStudioToolFile>
