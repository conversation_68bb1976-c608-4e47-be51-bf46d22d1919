// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "winresrc.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Neutral (Default) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_NEUD)
#ifdef _WIN32
LANGUAGE LANG_NEUTRAL, SUBLANG_DEFAULT
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

WINAMP DIALOG  32, 32, 217, 181
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "WavPack Configuration"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "Use Correction Files (.wvc) For Lossless Hybrid Playback",IDC_USEWVC,
                    "Button",BS_AUTOCHECKBOX | WS_TABSTOP,10,10,200,15
    CONTROL         "Always Output 16-bit Audio",IDC_ALWAYS_16BIT,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,10,25,200,15
    CONTROL         "Allow Multichannel",IDC_MULTICHANNEL,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,10,40,200,15
    GROUPBOX        "ReplayGain Configuration",IDC_RG_GROUP,10,60,200,75
    LTEXT           "ReplayGain Mode:",IDC_REPLAYGAIN_TEXT,20,75,65,10,SS_CENTERIMAGE
    COMBOBOX        IDC_REPLAYGAIN,90,75,110,60,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    LTEXT           "If Track Then Clips:",IDC_CLIPPING_TEXT,20,95,65,10,SS_CENTERIMAGE
    COMBOBOX        IDC_CLIPPING,90,94,110,55,CBS_DROPDOWNLIST | WS_VSCROLL | WS_TABSTOP
    CONTROL         "Use 24-bit Output For ReplayGain",IDC_24BIT_RG,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,25,115,165,15
    DEFPUSHBUTTON   "OK",IDOK,45,150,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,115,150,50,14
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO 
BEGIN
    "WINAMP", DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 210
        TOPMARGIN, 7
        BOTTOMMARGIN, 174
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE 
BEGIN
    IDS_DISABLED            "disabled"
    IDS_USE_TRACK           "use track gain"
    IDS_USE_ALBUM           "use album gain"
    IDS_JUST_CLIP           "just clip peaks"
    IDS_SOFT_CLIP           "softly clip peaks"
    IDS_PREVENT_CLIP        "scale track to prevent clips"
END

STRINGTABLE 
BEGIN
    IDS_ABOUT               "About WavPack Decoder"
    IDS_FORMAT              "WavPack Decoder Version %s \nCopyright (c) %d David Bryant"
    IDS_ENCODER_VERSION     "WavPack encoder version:  %d\n"
    IDS_SOURCE              "Source:  %d-bit %s at %d Hz \n"
    IDS_MULTICHANNEL        "Channels: %d (multichannel)\n"
    IDS_MONO                "Channels: 1 (mono)\n"
    IDS_STEREO              "Channels: 2 (stereo)\n"
    IDS_HYBRID              "hybrid"
    IDS_LOSSLESS            "lossless"
    IDS_LOSSY               "lossy"
    IDS_INTS                "ints"
    IDS_FLOATS              "floats"
    IDS_MODES               "Modes"
    IDS_FAST                ", fast"
    IDS_HIGH                ", high"
    IDS_VHIGH               ", v.high"
END

STRINGTABLE 
BEGIN
    IDS_EXTRA               ", extra"
    IDS_BITRATE             "Average bitrate"
    IDS_RATIO               "Overall ratio"
    IDS_KBPS                "kbps"
    IDS_MD5                 "Original md5"
    IDS_DESCRIPTION         "WavPack Decoder %s"
    IDS_FILETYPE            "WavPack Files (*.WV)"
END

STRINGTABLE 
BEGIN
    IDS_GUID                "{6DE2E465-690E-4df1-B6E2-2A9B33ED3DBB}"
END

#endif    // Neutral (Default) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""winresrc.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

