//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by wavpack.rc
//
#define IDS_DISABLED                    106
#define IDS_USE_TRACK                   107
#define IDS_USE_ALBUM                   108
#define IDS_JUST_CLIP                   109
#define IDS_SOFT_CLIP                   110
#define IDS_PREVENT_CLIP                111
#define IDS_ABOUT                       112
#define IDS_FORMAT                      113
#define IDS_ENCODER_VERSION             114
#define IDS_SOURCE                      115
#define IDS_MULTICHANNEL                116
#define IDS_MONO                        117
#define IDS_STEREO                      118
#define IDS_HYBRID                      119
#define IDS_LOSSLESS                    120
#define IDS_LOSSY                       121
#define IDS_INTS                        122
#define IDS_FLOATS                      123
#define IDS_MODES                       124
#define IDS_FAST                        125
#define IDS_HIGH                        126
#define IDS_VHIGH                       127
#define IDS_EXTRA                       128
#define IDS_BITRATE                     129
#define IDS_RATIO                       130
#define IDS_KBPS                        131
#define IDS_MD5                         132
#define IDS_DESCRIPTION                 133
#define IDS_STRING134                   134
#define IDS_FILETYPE                    134
#define IDC_RG_GROUP                    1017
#define IDC_USEWVC                      1018
#define IDC_REPLAYGAIN_TEXT             1019
#define IDC_REPLAYGAIN                  1020
#define IDC_CLIPPING_TEXT               1021
#define IDC_CLIPPING                    1022
#define IDC_ALWAYS_16BIT                1023
#define IDC_MULTICHANNEL                1024
#define IDC_24BIT_RG                    1025
#define IDS_GUID                        65535

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NO_MFC                     1
#define _APS_NEXT_RESOURCE_VALUE        135
#define _APS_NEXT_COMMAND_VALUE         40002
#define _APS_NEXT_CONTROL_VALUE         1027
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
