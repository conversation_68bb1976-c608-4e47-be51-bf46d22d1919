#ifndef __WASABI_TYPES_H
#define __WASABI_TYPES_H

// first, some standard int types
typedef unsigned int UINT;
typedef signed int SINT;

typedef unsigned char UCHAR;
typedef signed char <PERSON>HAR;

typedef unsigned long ARGB32;
typedef unsigned long <PERSON>GB32;

typedef unsigned long <PERSON>GB24;
typedef unsigned long RGB24;

typedef unsigned short <PERSON>GB16;
typedef unsigned short RGB16;

typedef unsigned long FOURCC;

#ifndef GUID_DEFINED
  #define GUID_DEFINED

  typedef struct _GUID 
	{
		unsigned long Data1;
		unsigned short Data2;
		unsigned short Data3;
		unsigned char Data4[8];
	} GUID;
/*
#ifndef _REFCLSID_DEFINED
#define REFGUID const GUID &
#define _REFCLSID_DEFINED
#endif
*/
#endif

#if defined(_MSC_VER) && _MSC_VER < 1600
#include <stddef.h>
	// since windows doesn't have stdint.h
	typedef unsigned __int64 uint64_t;
	typedef unsigned __int32 uint32_t;
	typedef unsigned __int16 uint16_t;
	typedef unsigned __int8 uint8_t;
	typedef __int64 int64_t;
	typedef __int32 int32_t;
	typedef __int16 int16_t;
	typedef __int8  int8_t;
	typedef size_t ssize_t;
#else
#include <stdint.h>
#include <stddef.h>
#include <inttypes.h>
#endif

#ifdef _WIN32
#include <windows.h>
// this is for GUID == and !=
#include <objbase.h>
#ifndef GUID_EQUALS_DEFINED
  #define GUID_EQUALS_DEFINED
#endif


#ifdef NULL
  #undef NULL
#endif
#ifndef NULL
  #define NULL 0
#endif

#ifdef _WIN32_WCE
typedef int intptr_t;
#endif
#endif
#endif
