# Copyright 2023 DeepMind Technologies Limited
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================


from __future__ import annotations

import argparse

from collections.abc import Generator
import functools
import time
from typing import Callable

from absl import flags
from absl import logging
import tqdm
import sys
import constants
import data_loaders
from utils import ac_utils
import compressor


_COMPRESSOR = flags.DEFINE_enum(
    'compressor',
    'gzip',
    compressor.COMPRESS_FN_DICT.keys(),
    'Compressor to use.',
)
_DATASET = flags.DEFINE_enum(
    'dataset',
    'imagenet',
    data_loaders.GET_DATA_GENERATOR_FN_DICT.keys(),
    'Dataset to use.',
)
_NUM_CHUNKS = flags.DEFINE_integer(
    'num_chunks',
    constants.NUM_CHUNKS,
    'Number of chunks.',
)
_VIDEO_PATH = flags.DEFINE_string(
    'video_path',
    None,
    'compress video path',
)


def evaluate_compressor_chunked(
        compress_fn: compressor.Compressor,
        get_data_generator_fn: Callable[[], Generator[bytes, None, None]],
        num_chunks: int,
        count_header_only_once: bool = True,
        mask_fn: Callable[[bytes], tuple[bytes, int]] | None = None,
        total: int = None,
        use_tqdm: bool = True,
        pbar: tqdm.tqdm = None
) -> tuple[float, float]:

    num_missed_bits = running_time = raw_length = compressed_length = 0

    data_generator = get_data_generator_fn()

    print_count = 1

    with tqdm.tqdm(data_generator, file=sys.stdout) as pbar:
        for i, (data, frame_id) in enumerate(pbar):

            if mask_fn is not None:
                data, missed_bits = mask_fn(data)
                num_missed_bits += missed_bits
            t0 = time.perf_counter()
            compressed_data = compress_fn(data)
            t1 = time.perf_counter()

            running_time += t1 - t0

            if isinstance(data, tuple):
                h, w = data[0].shape[:2]
            else:
                h, w = data.shape[:2]
            raw_length += h * w

            compressed_length += len(compressed_data[0])

            if print_count % 100 == 0:
                tqdm.tqdm.write(f"\ncompression rate: {compressed_length/raw_length}")
                print(f"compression factor: {raw_length/compressed_length}")
                print(f"raw_length: {raw_length}")
                print(f"compressed length: {compressed_length}")

            print_count += 1

    if mask_fn is not None:
        num_bits = 8 * num_chunks * constants.CHUNK_SIZE_BYTES
        compressed_length *= num_bits / (num_bits - num_missed_bits)


    if count_header_only_once:
        header_length = len(compress_fn((0).to_bytes(1, 'little')))
        compressed_length -= header_length * (num_chunks - 1)

    return compressed_length / raw_length, running_time


def evaluate_compressor_unchunked(
        compress_fn: compressor.Compressor,
        get_data_generator_fn: Callable[[], Generator[bytes, None, None]],
        num_chunks: int,
) -> tuple[float, float]:

    all_data = bytearray()
    for data in tqdm.tqdm(get_data_generator_fn(), total=num_chunks):
        all_data += data
    all_data = bytes(all_data)

    t0 = time.perf_counter()
    compressed_data = compress_fn(all_data)
    t1 = time.perf_counter()

    return len(compressed_data) / len(all_data), t1 - t0


def main(args, get_model_func=None) -> None:
    args.compressor = "language_model"
    logging.info('Compressor: %s', args.compressor)
    logging.info('Dataset: %s', args.dataset)
    print(f'Total chunks: {args.total}')
    compress_fn = functools.partial(
        compressor.COMPRESS_FN_DICT[args.compressor],
        model_path=get_model_func
    )
    get_data_generator_fn = functools.partial(
        data_loaders.GET_DATA_GENERATOR_FN_DICT[args.dataset],
        num_chunks=constants.NUM_CHUNKS,
        is_channel_wised=True,
        is_seq=True,
        data_path=args.data_path
    )
    if args.compressor in compressor.COMPRESSOR_TYPES['classical']:
        unchunked_rate, unchunked_time = evaluate_compressor_unchunked(
            compress_fn=compress_fn,
            get_data_generator_fn=get_data_generator_fn,
            num_chunks=constants.NUM_CHUNKS,
        )
        chunked_rate, chunked_time = evaluate_compressor_chunked(
            compress_fn=compress_fn,
            get_data_generator_fn=get_data_generator_fn,
            num_chunks=constants.NUM_CHUNKS,
            count_header_only_once=True,
            mask_fn=None,
        )
        logging.info(
            'Unchunked: %.1f [%.1fs]', 100 * unchunked_rate, unchunked_time
        )
        logging.info('Chunked: %.1f [%.1fs]', 100 * chunked_rate, chunked_time)
    elif args.compressor in compressor.COMPRESSOR_TYPES['arithmetic_coding']:
        mask_fn = ac_utils.right_shift_bytes_by_one
        mask_fn = None
        chunked_rate, chunked_time = evaluate_compressor_chunked(
            compress_fn=compress_fn,
            get_data_generator_fn=get_data_generator_fn,
            num_chunks=constants.NUM_CHUNKS,
            count_header_only_once=False,
            mask_fn=mask_fn,
            total=args.total
        )
        logging.info('Chunked: %.1f [%.1fs]', 100 * chunked_rate, chunked_time)


def get_args():
    parser = argparse.ArgumentParser(description="Compress")
    parser.add_argument('--compressor', type=str, default='language_model', )
    parser.add_argument('--dataset', type=str )
    parser.add_argument('--data_path', type=str, default='')
    parser.add_argument('--total', type=int, default=None, )


    _args = parser.parse_args()
    return _args



if __name__ == '__main__':
    main(get_args())
