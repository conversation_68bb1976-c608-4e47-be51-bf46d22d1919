import os
import pdb
import sys

import numpy as np

# data
sys.path.append("/root/capsule")

# using LLM, give out probability distributions for tokens
import argparse
from array import array
from functools import partial
from glob import glob
import pandas
from data.model.bgpt.config import *
from data.model.bgpt.utils import bGPTLMHeadModel
from time import time
import torch
from torch.utils.data import DataLoader
from tqdm import tqdm
from transformers import AutoTokenizer, AutoModelForCausalLM, GPT2Config
from typing import Any, List, Iterator
import wave
from utils import arithmetic_coder
from utils import ac_utils


class LLM_Compressor:

    def __init__(
        self,
        data_path: str,
        data_type: str,
        model_path: str,
        lora_path: str,
        context_window_size: int,
        batch_size: int,
    ):
        self.data_type = data_type
        self.context_window_size = context_window_size
        print(context_window_size)
        self.batch_size = batch_size
        self.total_original_length = 0
        self.total_compressed_length = 0
        self.total_compressed_length_ac = 0
        self.total_loss = 0
        self.temp_count = 0
        self.data = []
        self.fs = []
        self.max_fs = 1

        self.device = "cuda:0" if torch.cuda.is_available() else "cpu"
        print(f"Working on device {self.device}")
        if data_type == "wav" or data_type == "video" or data_type == "bmp":
            # read
            if data_type == "wav":
                self.fs = glob(data_path + "*.wav")
            elif data_type == "bmp":
                self.fs = glob(data_path + "*.bmp")
            else:
                self.fs = glob(data_path + "*")

            self.fs = self.fs[: min(self.max_fs, len(self.fs))]

            ext = None

            for _, af in tqdm(enumerate(self.fs), total=len(self.fs)):
                bytes, ext = read_bytes(af)
                self.data.append(bytes)
            patch_config = GPT2Config(
                num_hidden_layers=PATCH_NUM_LAYERS,
                max_length=PATCH_LENGTH,
                max_position_embeddings=PATCH_LENGTH,
                hidden_size=HIDDEN_SIZE,
                n_head=HIDDEN_SIZE // 64,
                vocab_size=1,
            )
            byte_config = GPT2Config(
                num_hidden_layers=BYTE_NUM_LAYERS,
                max_length=PATCH_SIZE + 1,
                max_position_embeddings=PATCH_SIZE + 1,
                hidden_size=HIDDEN_SIZE,
                n_head=HIDDEN_SIZE // 64,
                vocab_size=256 + 1,
            )
            self.model = bGPTLMHeadModel(patch_config, byte_config)
            print(
                "Parameter Number: "
                + str(
                    sum(p.numel() for p in self.model.parameters() if p.requires_grad)
                )
            )

            checkpoint = torch.load(model_path, map_location=torch.device(self.device))
            self.model.load_state_dict(checkpoint["model"], strict=False)
            self.model = self.model.to(self.device)
            self.model.eval()

            self.appendix = {"ext": ext}
            self.collate = byte_collate
        
        elif data_type == "wav_ascii":
            self.fs = glob(data_path + "*.wav")
            self.fs = self.fs[:min(self.max_fs, len(self.fs))]

            for _, af in tqdm(enumerate(self.fs), total=len(self.fs)):
                with wave.open(af) as f:
                    metadata = f.getparams()
                    audio_frames = f.readframes(metadata.nframes)
                audio_str, missed_bits_str = wav_to_ascii(audio_frames)
                audio_str += missed_bits_str

                self.data.append(audio_str)

            from peft import PeftModel

            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path, device_map=self.device,
            )
            if lora_path != None:
                self.model = PeftModel.from_pretrained(
                    self.model, lora_path, device_map=self.device
                )
            self.model.eval()

            self.collate = tokenize_collate

            self.appendix = {"tokenizer": self.tokenizer}

        elif data_type == "csv":
            self.fs = glob(data_path + "*.csv")
            self.fs = self.fs[: min(self.max_fs, len(self.fs))]

            for _, tf in tqdm(enumerate(self.fs), total=len(self.fs)):
                df = pandas.read_csv(tf)
                text_str = ""
                for _, row in tqdm(df.iterrows(), total=len(df)):
                    text_str += row["TEXT"]

                self.data.append(text_str)

            from peft import PeftModel

            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            self.tokenizer.pad_token = self.tokenizer.eos_token
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path
            )
            self.model.to(self.device)


            self.model = PeftModel.from_pretrained(
                self.model, lora_path, device_map=self.device
            )
            self.model.eval()

            self.collate = tokenize_collate

            self.appendix = {"tokenizer": self.tokenizer}
        self.outer_idx = 0
        self.theoretical_cl = []
        self.inner_dataloader_iter = iter(
            DataLoader(
                split(self.data[0], context_window_size),
                batch_size=batch_size,
                shuffle=False,
                collate_fn=partial(
                    self.collate,
                    appendix=self.appendix,
                    device=self.device,
                ),
            )
        )

    def forward_next(self):
        try:
            batch = next(self.inner_dataloader_iter)
            attention_mask = (
                batch["masks"] if "masks" in batch else batch["attention_mask"]
            )
            input_ids = batch["patches"] if "patches" in batch else batch["input_ids"]

            with torch.no_grad():
                output = self.model(**batch)
                # output = self.model(**batch, labels=input_ids)
                logits = output.logits
                if output.loss is not None:
                    loss = output.loss
                    self.total_loss += loss

            # transfer to float32
            if self.data_type == "wav" or self.data_type == "video" or self.data_type == "bmp":
                # logits: (511, 17, 257)
                # Remove the last time step for each patch
                # Remove the prediction for the ending patch
                logits = logits[:-1, :-1, :]
                logits = logits.reshape(1, -1, 257)  # Flatten to (1, 510 * PATCH_SIZE, 257)

                # Adjust input_ids: Remove the first and last 16 tokens
                input_ids = input_ids[:, PATCH_SIZE:-PATCH_SIZE]  # (1, 510 * PATCH_SIZE)

                # Adjust attention_mask
                attention_mask = attention_mask.repeat_interleave(PATCH_SIZE, dim=1)
                attention_mask = attention_mask[:, PATCH_SIZE:-PATCH_SIZE]  # Align with input_ids

                shifted_logits = logits
                shifted_input_ids = input_ids
                shifted_attention_mask = attention_mask

            else:
                # Remove the first token from the target (input_ids) and the last token from the logits
                shifted_logits = logits[:, :-1, :]
                # Remove the last token prediction
                shifted_input_ids = input_ids[:, 1:]
                # Remove the first token as target
                shifted_attention_mask = attention_mask[:, 1:]
                # Align the mask with shifted_input_ids
            # Compute softmax probabilities
            probs = shifted_logits.softmax(
                dim=-1
            )  # Shape: (batch_size, seq_len - 1, vocab_size)
            # Gather the probabilities for target tokens
            pd = torch.gather(probs, -1, shifted_input_ids.unsqueeze(-1)).squeeze(-1)
            # Shape: (batch_size, seq_len - 1)
            # to avoid inf problem

            eps = 6e-8
            pd = pd.add_(eps)
            # Compute negative log probabilities
            neg_log_probs = -torch.log2(pd)  # Use natural log for standard cross-entropy
            # Mask and normalize
            masked_neg_log_probs = neg_log_probs * shifted_attention_mask
            batch_sums = torch.sum(masked_neg_log_probs, dim=1) / input_ids.size(0)
            # Store the batch losses
            self.theoretical_cl.extend(batch_sums.tolist())


            return batch, probs, pd

        except StopIteration:
            # annouce tcl
            theoretical_bits_total = sum(self.theoretical_cl)
            original_data = self.data[self.outer_idx]
            while original_data and original_data[-1] == 256:
                original_data = original_data[:-1]

            self.total_original_length += len(original_data)
            self.total_compressed_length += theoretical_bits_total / 8

            theoretical_compression_ratio = (
                len(original_data) / theoretical_bits_total * 8.0
            )
            print(f"Theoretical compression ratio: {theoretical_compression_ratio:.6f}")
            # print(f"ac compression ratio: {self.total_original_length / self.total_compressed_length_ac:.6f}")
            self.theoretical_cl.clear()

            # build a new iter
            self.outer_idx += 1
            if self.outer_idx < len(self.data):
                self.inner_dataloader_iter = iter(
                    DataLoader(
                        split(self.data[self.outer_idx], self.context_window_size),
                        batch_size=self.batch_size,
                        shuffle=False,
                        collate_fn=partial(
                            self.collate,
                            appendix=self.appendix,
                            device=self.device,
                        ),
                    )
                )
                return f"End of file {self.fs[self.outer_idx - 1]}", theoretical_compression_ratio, None
            else:
                return None, None, None


def compress_with_llm(
    data_path: str,
    data_type: str,
    model_path: str,
    lora_path: str,
    context_window_size: int,
    batch_size: int,
) -> List[Any]:

    compressor = LLM_Compressor(
        data_path=data_path,
        data_type=data_type,
        model_path=model_path,
        lora_path=lora_path,
        context_window_size=context_window_size,
        batch_size=batch_size,
    )

    # 3. forward
    start = time()
    batch, probs, pd = compressor.forward_next()
    rs = []
    while batch is not None and probs is not None:
        if isinstance(batch, str):
            rs.append(probs)
        batch, probs, pd = compressor.forward_next()
        # if not isinstance(batch, str):
        #     encoder_decoder(batch, probs, pd, compressor)

    print(f"Total size: {compressor.total_original_length}\n"
          f"compressed size: {compressor.total_compressed_length}\n"
          f"total compression ratio: {compressor.total_original_length / compressor.total_compressed_length:.6f}"
          f"total loss: {compressor.total_loss}")

    end = time()
    # print(sum(rs) / len(rs))
    print(f"time cost {end - start}")

def encoder_decoder(batch, probs, pd, compressor):
    output = []
    encoder = arithmetic_coder.Encoder(
        base=2,
        precision=32,
        output_fn=output.append,
    )

    input_ids =  batch["patches"][:, PATCH_SIZE:-PATCH_SIZE] if "patches" in batch else batch["input_ids"]
    start_symbol = input_ids[:, :1]

    sequence_array = input_ids[:, 1:].detach().cpu().numpy().reshape(-1)
    probs = np.vstack(probs.detach().cpu().numpy().squeeze())
    pd = pd.squeeze()
    for symbol, prob, pd_prob in zip(sequence_array, probs, pd):
        encoder.encode(compression_utils.normalize_pdf_for_arithmetic_coding(prob), symbol)

    compressed_bits = ''.join(map(str, output))
    compressed_bytes, num_padded_bits = compression_utils.bits_to_bytes(compressed_bits)
    compressor.total_compressed_length_ac += len(compressed_bytes) + num_padded_bits
    print(f"compressed length: {len(compressed_bytes)}")
    print(f"compression ratio: {compressor.total_original_length / compressor.total_compressed_length_ac:.6f}")

    data_iter = iter(compression_utils.bytes_to_bits(compressed_bytes, num_padded_bits=num_padded_bits))
    def _input_fn(bit_sequence: Iterator[str] = data_iter) -> int | None:
        try:
            return int(next(bit_sequence))
        except StopIteration:
            return None

    decoder = arithmetic_coder.Decoder(
        base=2,
        precision=32,
        input_fn=_input_fn,
    )
    if isinstance(compressor.model, bGPTLMHeadModel):

        start_symbol = start_symbol.cpu().numpy().tolist()
        start_symbol_patch = get_pad_bgpt_input(start_symbol, compressor)

        logits = compressor.model(**start_symbol_patch).logits

        logits = logits[:-1, :-1, :]
        prob_de = logits.reshape(1, -1, 257).softmax(-1).detach().cpu().numpy().squeeze(axis=0)
        sequence_array_de = np.array(start_symbol)

    else:
        prob_de = compressor.model(start_symbol).logits.to(torch.float32).softmax(-1).detach().cpu().numpy().squeeze(axis=0)

        sequence_array_de = start_symbol.detach().cpu().numpy()

    start = time()
    for idx in tqdm(range(len(sequence_array))):
        de_token = decoder.decode(compression_utils.normalize_pdf_for_arithmetic_coding(prob_de[idx]))
        sequence_array_de = np.append(sequence_array_de, de_token)


        sequence_array_de_tensor = torch.tensor(sequence_array_de).unsqueeze(dim=0).to(compressor.device)
        with torch.no_grad():
            if  isinstance(compressor.model, bGPTLMHeadModel):
                sequence_array_de = sequence_array_de[None, :].tolist()
                sequence_array_de_input = get_pad_bgpt_input(sequence_array_de, compressor)
                logits = compressor.model(**sequence_array_de_input).logits
                logits = logits[:-1, :-1, :]
                prob_de = logits.reshape(1, -1, 257).softmax(-1).detach().cpu().numpy().squeeze(axis=0)
            else:
                pad_length = max(0, input_ids.shape[1]  - idx - 2)
                padded_decoder_input = torch.cat([sequence_array_de_tensor, torch.full((input_ids.shape[0], pad_length),  compressor.tokenizer.pad_token_id).to(compressor.device)], dim=1)
                prob_de = compressor.model(padded_decoder_input, ).logits.to(torch.float32).softmax(-1).detach().cpu().numpy().squeeze()


        print(f"decode token {de_token}, raw token: {input_ids[0, idx + 1]}")
    raw_data = compressor.tokenizer.decode(input_ids[0])
    decode_data = compressor.tokenizer.decode(sequence_array_de)
    end = time()

    print(f"raw_data: {raw_data} \n decode_data: {decode_data} \n time cost {end - start}s")
def split(s: List[Any], max_length: int) -> List[Any]:
    return [s[i : i + max_length] for i in range(0, len(s), max_length)]


def tokenize_collate(batch, appendix, device):
    tokenizer = appendix["tokenizer"]
    outputs = tokenizer(
        list(batch), padding="longest", truncation=False, return_tensors="pt"
    )

    return {k: v.to(device) for k, v in outputs.items()}


def byte_collate(batch, appendix, device):
    ext = appendix["ext"]
    # 1. find loggest
    max_length = max(len(b) for b in batch) + 2 * PATCH_SIZE

    padded_bytes = []
    padded_masks = []

    # 2. padding
    for b in batch:
        bos_patch = ext + [256] * (PATCH_SIZE - len(ext))
        b = bos_patch + b + [256] * PATCH_SIZE

        valid_length = len(b)
        padded_bytes.append(b + [256] * (max_length - valid_length))

        # Generate patch-level masks
        # Each patch contains PATCH_SIZE bytes, so we need (valid_length // PATCH_SIZE) masks
        patch_count = (valid_length + PATCH_SIZE - 1) // PATCH_SIZE  # Ceiling division
        total_patches = (
            max_length + PATCH_SIZE - 1
        ) // PATCH_SIZE  # Total number of patches after padding
        patch_masks = [1] * patch_count + [0] * (
            total_patches - patch_count
        )  # Active patches + padded patches
        padded_masks.append(patch_masks)

    patches = torch.tensor(padded_bytes, dtype=torch.long)
    masks = torch.tensor(padded_masks, dtype=torch.long)

    return {"patches": patches.to(device), "masks": masks.to(device)}


def get_pad_bgpt_input(batch, compressor):
    ext = compressor.appendix["ext"]
    # 1. find loggest
    max_length = max(len(b) for b in batch) + 2 * PATCH_SIZE

    padded_bytes = []
    padded_masks = []

    # 2. padding
    for b in batch:
        if len(b)< compressor.context_window_size:
            b = b + [256] * (compressor.context_window_size - len(b))
        bos_patch = ext + [256] * (PATCH_SIZE - len(ext))
        b = bos_patch + b + [256] * PATCH_SIZE

        valid_length = len(b)
        padded_bytes.append(b + [256] * (max_length - valid_length))

        # Generate patch-level masks
        # Each patch contains PATCH_SIZE bytes, so we need (valid_length // PATCH_SIZE) masks
        patch_count = (valid_length + PATCH_SIZE - 1) // PATCH_SIZE  # Ceiling division
        total_patches = (
                                max_length + PATCH_SIZE - 1
                        ) // PATCH_SIZE  # Total number of patches after padding
        patch_masks = [1] * patch_count + [0] * (
                total_patches - patch_count
        )  # Active patches + padded patches
        padded_masks.append(patch_masks)

    patches = torch.tensor(padded_bytes, dtype=torch.long)
    masks = torch.tensor(padded_masks, dtype=torch.long)

    return {"patches": patches.to(compressor.device), "masks": masks.to(compressor.device)}

def read_bytes(filename):
    if len(filename.split('.')) >= 2:
        ext = filename.split('.')[-1]
    else:
        ext = 'bin'

    ext = bytearray(ext, "utf-8")
    ext = [byte for byte in ext][:PATCH_SIZE]
    with open(filename, "rb") as f:
        file_bytes = f.read()

    bytes = []
    for byte in file_bytes:
        bytes.append(byte)

    if len(bytes) % PATCH_SIZE != 0:
        bytes = bytes + [256] * (PATCH_SIZE - len(bytes) % PATCH_SIZE)

    return bytes, ext

def wav_to_ascii(audio_frames: bytes, compact_missed_bits: bool = True) -> str:
    audio_frames = array("B", audio_frames).tolist()
    audio_str = "".join([chr(x >> 1) for x in audio_frames])

    missed_bits = [x & 1 for x in audio_frames]

    if compact_missed_bits:
        missed_bits = pack_bits(missed_bits, 7)
        missed_bits_str = "".join([chr(x >> 1) for x in missed_bits])

    return audio_str, missed_bits_str


def pack_bits(bits: List[int], every: int) -> List[bytes]:
    while len(bits) % every != 0:
        bits.append(0)

    packed = []
    for i in range(0, len(bits), every):
        byte = 0
        for j in range(every):
            byte |= bits[i + j] << (every - 1 - j)
        packed.append(byte)
    return packed


if __name__ == "__main__":

    parser = argparse.ArgumentParser()
    parser.add_argument("--data_path", type=str, default=None)
    parser.add_argument("--data_type", type=str, default=None)
    parser.add_argument("--model_path", type=str, default=None)
    parser.add_argument("--lora_path", type=str, default=None)
    parser.add_argument("--context_window", type=int, default=2048)
    parser.add_argument("--batch_size", type=int, default=1)

    args = parser.parse_args()

    compress_with_llm(
        data_path=args.data_path,
        data_type=args.data_type,
        model_path=args.model_path,
        lora_path=args.lora_path,
        context_window_size=args.context_window,
        batch_size=args.batch_size,
    )

"""
    configs:
    data_type: audio -> wav/wav_ascii; text -> csv
    model base: model/Llama-3-8B/
    audio lora model: model/Llama-3-8B_lora/Llama-3-8B_8bit_lora_ft
    medal lora model: model/Llama-3-8B_lora/Llama-3-8B_8bit_lora_ft_medal
    eurlex lora model: model/Llama-3-8B_lora/Llama-3-8B_8bit_lora_ft_eurlex

    bGPT: ./third_party/bgpt/pretrained_model/bgpt/weights-audio.pth"
"""
