import argparse
import os
import tempfile
import subprocess
import shutil
from pathlib import Path
import time
import brotli
from glob import glob
from tqdm import tqdm
from PIL import Image
import natsort

def compress_text(
    input_dir,
    compression_type="brotli",
    save_output=False,
    output_dir=None,
):
    """
    Compress text files and calculate compression ratio
    Args:
        input_dir: Input directory path
        compression_type: 'brotli', 'zpaq', or '7z'
        save_output: Whether to save compressed files
        output_dir: Output directory path for compressed files (if save_output is True)
    """
    # Check if compression tools are installed
    required_tools = {
        "zpaq": "ZPAQ",
        "7z": "7-Zip",
        "zstd": "Zstandard",
        "ppmd": "PPM",
    }
    tool_commands = {"zpaq": "zpaq", "7z": "7zz", "zstd": "zstd", "ppm": "ppm"}

    if (
        compression_type in tool_commands
        and shutil.which(tool_commands[compression_type]) is None
    ):
        print(f"{required_tools[compression_type]} not found, please install it first")
        return

    # Check and create output directory if saving files
    if save_output:
        if output_dir is None:
            print("Output directory path is required when save_output is True")
            return
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

    total_original_size = 0
    total_compressed_size = 0
    total_time = 0
    processed_files = 0

    # Get total original size of all text files first
    all_text_files = [
        f
        for f in os.listdir(input_dir)
        if f.lower().endswith((".txt", ".log", ".md", ".json", ".xml", ".csv", ".y4m"))
    ]
    for filename in all_text_files:
        file_path = os.path.join(input_dir, filename)
        total_original_size += os.path.getsize(file_path)

    # Use temporary directory for compression
    with tempfile.TemporaryDirectory() as temp_dir:
        for filename in all_text_files:
            file_path = os.path.join(input_dir, filename)
            base_name = Path(filename).stem

            # Get original file size
            original_size = os.path.getsize(file_path)

            # Set file extension and compression command based on compression type
            if compression_type == "brotli":
                ext = ".br"
                compressed_path = os.path.join(temp_dir, base_name + ext)

                # Use brotli package for compression
                start_time = time.time()
                try:
                    with open(file_path, "rb") as infile:
                        with open(compressed_path, "wb") as outfile:
                            outfile.write(
                                brotli.compress(infile.read(), quality=11)
                            )  # maximum quality
                    end_time = time.time()
                except Exception as e:
                    print(f"Error compressing file {filename}: {e}")
                    continue

            elif compression_type == "zpaq":
                ext = ".zpaq"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "zpaq",
                    "add",
                    compressed_path,
                    file_path,
                    "-method",
                    "5",  # maximum compression
                    "-threads",
                    "1",  # single thread
                ]

                start_time = time.time()
                try:
                    subprocess.run(
                        compress_cmd,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        check=True,
                        creationflags=(
                            subprocess.CREATE_NO_WINDOW if os.name == "nt" else 0
                        ),
                    )
                    end_time = time.time()
                except subprocess.CalledProcessError as e:
                    print(f"Error compressing file {filename}: {e}")
                    continue

            elif compression_type == "7z":
                ext = ".7z"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "7zz",
                    "a",
                    "-mx=9",  # maximum compression
                    "-mmt=1",  # single thread
                    compressed_path,
                    file_path,
                ]
            elif compression_type == "zstd":
                ext = ".zst"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "zstd",
                    "-22",  # highest compression level (1-19)
                    "--ultra",  # enable ultra compression (allows levels up to 22)
                    "-T1",  # single thread for fair comparison
                    "-f",  # force overwrite
                    file_path,
                    "-o",
                    compressed_path,
                ]

            elif compression_type == "ppm":
                ext = ".ppm"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "ppmd",
                    "c",
                    file_path,
                    compressed_path
                ]

            start_time = time.time()
            try:
                subprocess.run(
                    compress_cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    check=True,
                    creationflags=(
                        subprocess.CREATE_NO_WINDOW if os.name == "nt" else 0
                    ),
                )
                end_time = time.time()
            except subprocess.CalledProcessError as e:
                print(f"Error compressing file {filename}: {e}")
                continue

            compression_time = end_time - start_time

            # Get compressed file size
            compressed_size = os.path.getsize(compressed_path)
            total_compressed_size += compressed_size

            # Calculate compression ratio for individual file
            compression_ratio = compressed_size / original_size * 100

            print(f"\nProcessing: {filename}")
            print(f"Original size: {original_size/1024:.2f} KB")
            print(f"Compressed size: {compressed_size/1024:.2f} KB")
            print(f"Compression ratio: {compression_ratio:.2f}%")
            print(f"Time taken: {compression_time:.2f} seconds")
            print(
                f"Compression speed: {(original_size/1024)/compression_time:.2f} KB/s"
            )
            print(f"Format: {compression_type.upper()}")

            # Copy to output directory if saving is enabled
            if save_output:
                output_path = output_dir / f"{base_name}{ext}"
                shutil.copy2(compressed_path, output_path)
                print(f"Saved to: {output_path}")

            total_time += compression_time
            processed_files += 1

    if processed_files > 0:
        # Calculate overall compression ratio for the entire folder
        overall_compression_ratio = total_compressed_size / total_original_size * 100
        average_time = total_time / processed_files

        print(f"\nFolder-wide Summary for {compression_type.upper()}:")
        print(f"Total files processed: {processed_files}")
        print(f"Total original size: {total_original_size/1024:.2f} KB")
        print(f"Total compressed size: {total_compressed_size/1024:.2f} KB")
        print(f"Overall compression ratio: {overall_compression_ratio:.2f}%")
        print(f"Average processing time: {average_time:.2f} seconds per file")
        print(f"Total processing time: {total_time:.2f} seconds")
    else:
        print("\nNo text files found in the input directory")


def compress_audio(
    input_dir,
    compression_type="flac",
    save_output=False,
    output_dir=None,
    compression_level=None,
):
    """
    Compress audio files and calculate compression ratio
    Args:
        input_dir: Input directory path
        compression_type: 'flac', 'ofr' (OptimFROG), or 'wavpack'
        save_output: Whether to save compressed files
        output_dir: Output directory path for compressed files (if save_output is True)
        compression_level: Compression level (optional)
            - FLAC: 0-12
            - OptimFROG: ignored (always uses max)
            - WavPack: 1-6 (fast-highest)
    """
    # Set default compression levels
    default_levels = {
        "flac": 12,  # maximum compression
    }

    if compression_level is None:
        compression_level = default_levels.get(compression_type)

    # Check if compression tools are installed
    required_tools = {"ofr": "OptimFROG", "flac": "FFmpeg", "wavpack": "WavPack"}

    tool_commands = {"ofr": "ofr", "flac": "ffmpeg", "wavpack": "wavpack"}

    if compression_type not in tool_commands:
        print(f"Unsupported compression type: {compression_type}")
        print(f"Supported types: {', '.join(tool_commands.keys())}")
        return

    if shutil.which(tool_commands[compression_type]) is None:
        print(f"{required_tools[compression_type]} not found, please install it first")
        return

    # Check and create output directory if saving files
    if save_output:
        if output_dir is None:
            print("Output directory path is required when save_output is True")
            return
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

    total_original_size = 0
    total_compressed_size = 0
    total_time = 0
    processed_files = 0

    # Get total original size of all WAV files first
    all_wav_files = [f for f in os.listdir(input_dir) if f.lower().endswith(".wav")]
    for filename in all_wav_files:
        wav_path = os.path.join(input_dir, filename)
        total_original_size += os.path.getsize(wav_path)

    # Use temporary directory for compression
    with tempfile.TemporaryDirectory() as temp_dir:
        for filename in all_wav_files:
            wav_path = os.path.join(input_dir, filename)
            base_name = Path(filename).stem

            # Get original file size
            original_size = os.path.getsize(wav_path)

            # Set file extension and compression command based on compression type
            if compression_type == "flac":
                ext = ".flac"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "ffmpeg",
                    "-i",
                    wav_path,
                    "-c:a",
                    "flac",
                    "-compression_level",
                    str(compression_level),
                    "-y",
                    compressed_path,
                ]

            elif compression_type == "ofr":
                ext = ".ofr"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "ofr",
                    "--encode",
                    "--preset",
                    "max",
                    wav_path,
                    "--output",
                    compressed_path,
                ]

            elif compression_type == "wavpack":
                ext = ".wv"
                compressed_path = os.path.join(temp_dir, base_name + ext)
                compress_cmd = [
                    "wavpack",
                    "-hh",
                    "-x3",
                    "--threads=1",
                    wav_path,
                    "-o",
                    compressed_path,
                ]

            # Execute compression and measure time
            print(f"\nProcessing: {filename}")
            print(f"Command: {' '.join(compress_cmd)}")
            start_time = time.time()

            try:
                subprocess.run(
                    compress_cmd,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    check=True,
                    creationflags=subprocess.CREATE_NO_WINDOW if os.name == "nt" else 0,
                )
            except subprocess.CalledProcessError as e:
                print(f"Error compressing file {filename}: {e}")
                continue

            end_time = time.time()
            compression_time = end_time - start_time

            # Get compressed file size
            compressed_size = os.path.getsize(compressed_path)
            total_compressed_size += compressed_size

            # Calculate compression ratio for individual file
            compression_ratio = compressed_size / original_size * 100

            print(f"Original size: {original_size/1024/1024:.2f} MB")
            print(f"Compressed size: {compressed_size/1024/1024:.2f} MB")
            print(f"Compression ratio: {compression_ratio:.2f}%")
            print(f"Time taken: {compression_time:.2f} seconds")
            print(
                f"Compression speed: {(original_size/1024/1024)/compression_time:.2f} MB/s"
            )
            print(f"Format: {compression_type.upper()}")
            if compression_type not in ["ofr"]:
                print(f"Compression level: {compression_level}")

            # Copy to output directory if saving is enabled
            if save_output:
                output_path = output_dir / f"{base_name}{ext}"
                shutil.copy2(compressed_path, output_path)
                print(f"Saved to: {output_path}")

            total_time += compression_time
            processed_files += 1

    if processed_files > 0:
        # Calculate overall compression ratio for the entire folder
        overall_compression_ratio = total_compressed_size / total_original_size * 100
        average_time = total_time / processed_files

        print(f"\nFolder-wide Summary for {compression_type.upper()}:")
        print(f"Total files processed: {processed_files}")
        print(f"Total original size: {total_original_size/1024/1024:.2f} MB")
        print(f"Total compressed size: {total_compressed_size/1024/1024:.2f} MB")
        print(f"Overall compression ratio: {overall_compression_ratio:.2f}%")
        print(f"Average processing time: {average_time:.2f} seconds per file")
        print(f"Total processing time: {total_time:.2f} seconds")
    else:
        print("\nNo WAV files found in the input directory")

    # clean up
    try:
        shutil.rmtree(temp_dir)
    except:
        pass




def compress_img(method, data_root_path, save_name):
  data_path_list = natsort.natsorted(
      [os.path.join(data_root_path, path) for path in glob.glob(data_root_path + '/*.png')])
  data_name_list = [os.path.basename(item).replace('.png', '') for item in data_path_list]
  save_path = os.path.join(os.path.dirname(data_root_path), f"{method.lower()}", f"{save_name}")
  original_size = 0
  compressed_size = 0
  start_time = time.time()
  if not os.path.exists(save_path):
      os.makedirs(save_path)
  for data_path, data_name in tqdm.tqdm(zip(data_path_list, data_name_list), total=len(data_path_list)):
    if method == "webp":
      single_save_path = f"{save_path}/{data_name}.webp"
      image = Image.open(data_path)
      original_size += len(image.tobytes())
      image.save(single_save_path, format='WEBP', lossless=True)
    elif method == "png":
      single_save_path = f"{save_path}/{data_name}.png"
      image = Image.open(data_path)
      original_size += len(image.tobytes())
      image.save(single_save_path,  format='PNG', optimize=True)
    elif method == "jpegxl":
      single_save_path = f"{save_path}/{data_name}.jxl"
      image = Image.open(data_path)
      original_size += len(image.tobytes())
      image.save(single_save_path,  lossless=True)
    elif method == "jpeg2000":
      single_save_path = f"{save_path}/{data_name}.jp2"
      image = Image.open(data_path)
      original_size += len(image.tobytes())
      image.save(single_save_path, format='JPEG2000', lossless=True)
    else:
      raise NotImplementedError(f"Method {method} not implemented")
    compressed_size += os.path.getsize(single_save_path)

  end_time = time.time()
  cost_time = end_time - start_time

  compression_rate = (compressed_size / original_size) * 100
  print(f"{method}: {compressed_size} bytes, Compression rate: {compression_rate:.2f}%, Time: {cost_time:.2f} seconds")
  with open(f'./{method}_{save_name}_results', 'w') as f:
    f.write(f"{method}: {compressed_size} bytes, Compression rate: {compression_rate:.2f}%, Time: {cost_time:.2f} seconds\n"
            f"Original size: {original_size} bytes\n"
            f"Compressed size: {compressed_size} bytes\n")


if __name__ == "__main__":
    original_path = os.environ["PATH"]
    # add wavpack
    os.environ["PATH"] = "./third_party/WavPack/cli:" + original_path
    # add ffmpeg
    os.environ["PATH"] = (
        "./third_party/ffmpeg-git-20240629-amd64-static:" + os.environ["PATH"]
    )
    # add OptimFROG
    os.environ["PATH"] = "./third_party/OptimFROG_Linux_x64_5100:" + os.environ["PATH"]
    # add zpaq
    os.environ["PATH"] = "./third_party/zpaq:" + os.environ["PATH"]
    # add 7z
    os.environ["PATH"] = "./third_party/7z:" + os.environ["PATH"]
    # add zstd
    os.environ["PATH"] = "./third_party/zstd-1.5.6:" + os.environ["PATH"]
    # ad ppm
    os.environ["PATH"] = "./third_party/ppm:" + os.environ["PATH"]

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--mode",
        type=str,
        choices=["audio", "text"],
        default="audio",
        help="Compression mode: 'audio' or 'text'",
    )
    parser.add_argument("--input_dir", type=str, default=None)
    parser.add_argument("--output_dir", type=str, default=None)
    parser.add_argument("--do_output", action="store_true", default=False)
    parser.add_argument(
        "--formats",
        type=str,
        default="flac",
        help="Comma-separated list of formats. For audio: flac,ofr,wavpack. For text: brotli,zpaq,7z,zstd",
    )
    args = parser.parse_args()

    input_directory = args.input_dir
    output_directory = args.output_dir

    # Test all formats
    formats = args.formats.split(",")
    for format_type in formats:
        print(f"\nTesting {format_type.upper()} compression:")
        if args.mode == "audio":
            compress_audio(
                input_directory, format_type, args.do_output, output_directory
            )
        else:
            compress_text(
                input_directory, format_type, args.do_output, output_directory
            )
