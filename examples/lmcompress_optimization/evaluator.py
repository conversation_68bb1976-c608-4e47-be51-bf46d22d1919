"""
Evaluator for LMCompress Optimization using OpenEvolve
Measures compression efficiency, quality preservation, and algorithmic improvements
"""

import importlib.util
import numpy as np
import time
import concurrent.futures
import traceback
import json
from typing import Dict, List, Any, Tuple
import hashlib
import os
from collections import defaultdict

def run_with_timeout(func, args=(), kwargs={}, timeout_seconds=60):
    """Run a function with timeout protection."""
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(func, *args, **kwargs)
        try:
            return future.result(timeout=timeout_seconds)
        except concurrent.futures.TimeoutError:
            raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds} seconds")

def generate_multimodal_test_dataset(num_samples=15):
    """Generate comprehensive multimodal test dataset for LMCompress evaluation."""
    dataset = []
    
    for i in range(num_samples):
        np.random.seed(i * 42)  # Deterministic for reproducibility
        
        # Create diverse test scenarios
        scenario_type = i % 5
        
        if scenario_type == 0:  # High-detail images
            image = create_complex_image(128, 128)
            audio = create_speech_like_audio(16000)
            text = create_technical_text("medical")
            video = [create_complex_image(64, 64) for _ in range(8)]
            domain = "medical"
            
        elif scenario_type == 1:  # Simple patterns
            image = create_pattern_image(64, 64)
            audio = create_tone_audio(8000, 440)  # Pure tone
            text = create_simple_text()
            video = [create_pattern_image(32, 32) for _ in range(5)]
            domain = "general"
            
        elif scenario_type == 2:  # Legal documents
            image = create_document_image(96, 96)
            audio = create_speech_like_audio(12000)
            text = create_technical_text("legal")
            video = [create_document_image(48, 48) for _ in range(6)]
            domain = "legal"
            
        elif scenario_type == 3:  # High motion video
            image = create_noise_image(80, 80)
            audio = create_music_like_audio(10000)
            text = create_mixed_text()
            video = [create_motion_frame(40, 40, frame_idx) for frame_idx in range(12)]
            domain = "general"
            
        else:  # Mixed complexity
            image = create_mixed_complexity_image(100, 100)
            audio = create_mixed_audio(14000)
            text = create_technical_text("medical")
            video = [create_mixed_complexity_image(50, 50) for _ in range(10)]
            domain = "medical"
        
        dataset.append({
            'image': image,
            'audio': audio,
            'text': text,
            'video': video,
            'domain': domain,
            'scenario_type': scenario_type,
            'sample_id': i
        })
    
    return dataset

def create_complex_image(h, w):
    """Create a complex image with various features."""
    image = np.zeros((h, w, 3), dtype=np.uint8)
    
    # Add gradients
    for i in range(h):
        for j in range(w):
            image[i, j, 0] = int(255 * i / h)
            image[i, j, 1] = int(255 * j / w)
            image[i, j, 2] = int(255 * ((i + j) % 256) / 255)
    
    # Add noise
    noise = np.random.randint(0, 50, (h, w, 3))
    image = np.clip(image.astype(int) + noise, 0, 255).astype(np.uint8)
    
    return image

def create_pattern_image(h, w):
    """Create a simple pattern image."""
    image = np.zeros((h, w, 3), dtype=np.uint8)
    
    # Checkerboard pattern
    for i in range(h):
        for j in range(w):
            if (i // 8 + j // 8) % 2 == 0:
                image[i, j] = [255, 255, 255]
            else:
                image[i, j] = [0, 0, 0]
    
    return image

def create_document_image(h, w):
    """Create a document-like image."""
    image = np.ones((h, w, 3), dtype=np.uint8) * 240  # Light background
    
    # Add text-like patterns
    for i in range(5, h-5, 10):
        for j in range(5, w-5, 3):
            if np.random.random() > 0.3:
                image[i:i+2, j:j+8] = [0, 0, 0]  # Black text
    
    return image

def create_noise_image(h, w):
    """Create a noisy image."""
    return np.random.randint(0, 256, (h, w, 3), dtype=np.uint8)

def create_mixed_complexity_image(h, w):
    """Create an image with mixed complexity regions."""
    image = np.zeros((h, w, 3), dtype=np.uint8)
    
    # Simple region
    image[:h//2, :w//2] = create_pattern_image(h//2, w//2)
    
    # Complex region
    image[h//2:, w//2:] = create_complex_image(h//2, w//2)
    
    # Noise regions
    image[:h//2, w//2:] = create_noise_image(h//2, w//2)
    image[h//2:, :w//2] = create_document_image(h//2, w//2)
    
    return image

def create_motion_frame(h, w, frame_idx):
    """Create a frame with motion."""
    image = np.zeros((h, w, 3), dtype=np.uint8)
    
    # Moving circle
    center_x = int(w/2 + 10 * np.sin(frame_idx * 0.5))
    center_y = int(h/2 + 10 * np.cos(frame_idx * 0.5))
    
    for i in range(h):
        for j in range(w):
            dist = np.sqrt((i - center_y)**2 + (j - center_x)**2)
            if dist < 8:
                image[i, j] = [255, 0, 0]  # Red circle
    
    return image

def create_speech_like_audio(length):
    """Create speech-like audio signal."""
    t = np.linspace(0, length/16000, length)
    
    # Multiple frequency components like speech
    audio = (0.3 * np.sin(2 * np.pi * 200 * t) +
             0.2 * np.sin(2 * np.pi * 800 * t) +
             0.1 * np.sin(2 * np.pi * 1600 * t) +
             0.05 * np.random.randn(length))
    
    # Add amplitude modulation
    audio *= (0.5 + 0.5 * np.sin(2 * np.pi * 5 * t))
    
    return audio.astype(np.float32)

def create_tone_audio(length, frequency):
    """Create a pure tone."""
    t = np.linspace(0, length/16000, length)
    audio = 0.5 * np.sin(2 * np.pi * frequency * t)
    return audio.astype(np.float32)

def create_music_like_audio(length):
    """Create music-like audio with harmonics."""
    t = np.linspace(0, length/16000, length)
    
    # Fundamental and harmonics
    audio = (0.4 * np.sin(2 * np.pi * 440 * t) +
             0.2 * np.sin(2 * np.pi * 880 * t) +
             0.1 * np.sin(2 * np.pi * 1320 * t) +
             0.05 * np.sin(2 * np.pi * 1760 * t))
    
    return audio.astype(np.float32)

def create_mixed_audio(length):
    """Create mixed audio with varying characteristics."""
    half = length // 2
    audio1 = create_speech_like_audio(half)
    audio2 = create_tone_audio(length - half, 880)
    return np.concatenate([audio1, audio2])

def create_technical_text(domain):
    """Create domain-specific technical text."""
    if domain == "medical":
        return ("The patient presented with acute myocardial infarction and underwent percutaneous coronary intervention. "
                "Electrocardiogram showed ST-elevation in leads V2-V6. Troponin levels were significantly elevated. "
                "Echocardiography revealed left ventricular dysfunction with ejection fraction of 35%. "
                "Treatment included dual antiplatelet therapy, beta-blockers, and ACE inhibitors.")
    elif domain == "legal":
        return ("Pursuant to Section 12(a) of the Securities Exchange Act of 1934, the defendant is liable for material "
                "misstatements in the registration statement. The plaintiff seeks damages under Rule 10b-5 for securities fraud. "
                "The court must determine whether scienter has been adequately pleaded under the Private Securities Litigation Reform Act. "
                "Res ipsa loquitur applies to establish prima facie negligence in this matter.")
    else:
        return create_simple_text()

def create_simple_text():
    """Create simple, repetitive text."""
    return ("This is a simple test text. " * 10 + 
            "The quick brown fox jumps over the lazy dog. " * 5 +
            "Hello world, this is a compression test. " * 8)

def create_mixed_text():
    """Create text with mixed complexity."""
    return (create_simple_text() + " " + 
            create_technical_text("medical") + " " +
            "Random numbers: " + " ".join([str(np.random.randint(0, 1000)) for _ in range(20)]))

def calculate_compression_metrics(original_size, compressed_size, processing_time, quality_score):
    """Calculate comprehensive compression metrics."""
    if compressed_size == 0:
        return {
            'compression_ratio': 0,
            'compression_efficiency': 0,
            'quality_weighted_ratio': 0,
            'speed_efficiency': 0
        }
    
    compression_ratio = original_size / compressed_size
    compression_efficiency = compression_ratio / max(processing_time, 0.001)
    quality_weighted_ratio = compression_ratio * quality_score
    speed_efficiency = 1.0 / max(processing_time, 0.001)
    
    return {
        'compression_ratio': compression_ratio,
        'compression_efficiency': compression_efficiency,
        'quality_weighted_ratio': quality_weighted_ratio,
        'speed_efficiency': speed_efficiency
    }

def evaluate_lmcompress_performance(results, expected_baselines):
    """Evaluate LMCompress performance against expected baselines."""
    scores = {}
    
    # Extract overall metrics
    overall_metrics = results.get('overall_metrics', {})
    compression_ratio = overall_metrics.get('total_compression_ratio', 0)
    quality_score = overall_metrics.get('average_quality_score', 0)
    processing_time = overall_metrics.get('total_processing_time', float('inf'))
    
    # Compression ratio score (target: 5x improvement over traditional methods)
    target_ratio = expected_baselines.get('target_compression_ratio', 5.0)
    ratio_score = min(1.0, compression_ratio / target_ratio)
    
    # Quality preservation score (target: >0.8)
    quality_target = expected_baselines.get('target_quality', 0.8)
    quality_score_norm = min(1.0, quality_score / quality_target)
    
    # Speed efficiency score (target: <1s per modality)
    modalities = overall_metrics.get('modalities_processed', 1)
    target_time_per_modality = expected_baselines.get('target_time_per_modality', 1.0)
    time_per_modality = processing_time / modalities
    speed_score = min(1.0, target_time_per_modality / max(time_per_modality, 0.001))
    
    # Cross-modal consistency (bonus for processing multiple modalities well)
    modality_bonus = min(1.0, modalities / 4.0)  # Bonus for handling all 4 modalities
    
    # Algorithm sophistication score
    sophistication_score = evaluate_algorithm_sophistication(results)
    
    scores = {
        'compression_ratio_score': ratio_score,
        'quality_preservation_score': quality_score_norm,
        'speed_efficiency_score': speed_score,
        'cross_modal_bonus': modality_bonus,
        'algorithm_sophistication': sophistication_score
    }
    
    return scores

def evaluate_algorithm_sophistication(results):
    """Evaluate the sophistication of the compression algorithm."""
    sophistication_score = 0.0
    
    # Check for adaptive techniques
    compression_stats = results.get('compression_stats', {})
    
    for modality, stats in compression_stats.items():
        # Reward for processing multiple segments (shows segmentation)
        segments = stats.get('segments_processed', 0)
        if segments > 1:
            sophistication_score += 0.1
        
        # Reward for good compression ratios
        ratio = stats.get('compression_ratio', 0)
        if ratio > 3.0:
            sophistication_score += 0.1
        
        # Reward for quality preservation
        quality = stats.get('quality_score', 0)
        if quality > 0.7:
            sophistication_score += 0.1
    
    return min(1.0, sophistication_score)

def evaluate(program_path):
    """
    Main evaluation function for LMCompress optimization.
    """
    try:
        # Load the program
        spec = importlib.util.spec_from_file_location("program", program_path)
        program = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(program)
        
        # Check required function exists
        if not hasattr(program, "compress_multimodal_data"):
            return {
                "compression_ratio": 0.0,
                "quality_preservation": 0.0,
                "processing_speed": 0.0,
                "algorithm_sophistication": 0.0,
                "cross_modal_efficiency": 0.0,
                "overall_score": 0.0,
                "error": "Missing compress_multimodal_data function"
            }
        
        # Generate test dataset
        test_dataset = generate_multimodal_test_dataset(12)
        
        # Expected baselines (based on LMCompress paper results)
        expected_baselines = {
            'target_compression_ratio': 4.0,  # 4x improvement target
            'target_quality': 0.75,
            'target_time_per_modality': 2.0
        }
        
        # Evaluation metrics
        all_scores = []
        total_compression_ratios = []
        total_quality_scores = []
        total_processing_times = []
        success_count = 0
        
        for i, data_item in enumerate(test_dataset):
            try:
                # Process with timeout
                start_time = time.time()
                results = run_with_timeout(
                    program.compress_multimodal_data,
                    kwargs={
                        'image_data': data_item.get('image'),
                        'audio_data': data_item.get('audio'),
                        'text_data': data_item.get('text'),
                        'video_data': data_item.get('video'),
                        'domain': data_item.get('domain', 'general')
                    },
                    timeout_seconds=30
                )
                end_time = time.time()
                
                # Validate results
                if not isinstance(results, dict) or 'overall_metrics' not in results:
                    continue
                
                # Evaluate performance
                performance_scores = evaluate_lmcompress_performance(results, expected_baselines)
                all_scores.append(performance_scores)
                
                # Collect metrics
                overall_metrics = results['overall_metrics']
                total_compression_ratios.append(overall_metrics.get('total_compression_ratio', 0))
                total_quality_scores.append(overall_metrics.get('average_quality_score', 0))
                total_processing_times.append(overall_metrics.get('total_processing_time', 0))
                
                success_count += 1
                
            except Exception as e:
                print(f"Error processing sample {i}: {str(e)}")
                continue
        
        # Calculate final scores
        if success_count == 0:
            return {
                "compression_ratio": 0.0,
                "quality_preservation": 0.0,
                "processing_speed": 0.0,
                "algorithm_sophistication": 0.0,
                "cross_modal_efficiency": 0.0,
                "overall_score": 0.0,
                "success_rate": 0.0,
                "error": "All samples failed"
            }
        
        # Average scores across all samples
        avg_compression_ratio_score = np.mean([s['compression_ratio_score'] for s in all_scores])
        avg_quality_score = np.mean([s['quality_preservation_score'] for s in all_scores])
        avg_speed_score = np.mean([s['speed_efficiency_score'] for s in all_scores])
        avg_sophistication = np.mean([s['algorithm_sophistication'] for s in all_scores])
        avg_cross_modal = np.mean([s['cross_modal_bonus'] for s in all_scores])
        
        success_rate = success_count / len(test_dataset)
        
        # Calculate overall score with balanced weights
        overall_score = (
            0.30 * avg_compression_ratio_score +    # Compression efficiency
            0.25 * avg_quality_score +              # Quality preservation
            0.20 * avg_speed_score +                # Processing speed
            0.15 * avg_sophistication +             # Algorithm sophistication
            0.10 * avg_cross_modal                  # Cross-modal handling
        ) * success_rate  # Penalize failures
        
        return {
            "compression_ratio": float(np.mean(total_compression_ratios)),
            "quality_preservation": float(np.mean(total_quality_scores)),
            "processing_speed": float(1.0 / max(np.mean(total_processing_times), 0.001)),
            "algorithm_sophistication": float(avg_sophistication),
            "cross_modal_efficiency": float(avg_cross_modal),
            "overall_score": float(overall_score),
            "success_rate": float(success_rate),
            "samples_processed": int(success_count),
            "avg_compression_ratio": float(np.mean(total_compression_ratios)),
            "avg_processing_time": float(np.mean(total_processing_times))
        }
        
    except Exception as e:
        print(f"Evaluation failed: {str(e)}")
        print(traceback.format_exc())
        return {
            "compression_ratio": 0.0,
            "quality_preservation": 0.0,
            "processing_speed": 0.0,
            "algorithm_sophistication": 0.0,
            "cross_modal_efficiency": 0.0,
            "overall_score": 0.0,
            "error": str(e)
        }
