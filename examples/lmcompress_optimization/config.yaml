# Configuration for LMCompress Optimization using OpenEvolve
# Based on "Understanding is Compression" research paper

max_iterations: 30
checkpoint_interval: 10
log_level: "INFO"

# LLM configuration optimized for compression algorithm development
llm:
  primary_model: "gemini-2.0-flash-lite"  # Fast model for frequent iterations
  primary_model_weight: 0.75
  secondary_model: "gemini-2.0-flash"     # Higher quality model for complex optimizations
  secondary_model_weight: 0.25
  api_base: "https://generativelanguage.googleapis.com/v1beta/openai/"
  api_key: "AIzaSyAr-5UzASN7jNott9_4TxUvXPa_gTam1_o"
  temperature: 0.75  # Balanced creativity for algorithmic improvements
  top_p: 0.95
  max_tokens: 5120   # Sufficient context for complex compression algorithms

# Specialized prompt configuration for LMCompress optimization
prompt:
  system_message: |
    You are an expert in data compression algorithms, information theory, and large language model optimization. 
    Your task is to evolve and improve the LMCompress algorithm implementation based on the "Understanding is Compression" research paper.
    
    Focus on optimizing:
    1. Compression ratios while preserving quality (target: 4-6x improvement over traditional methods)
    2. Processing speed and computational efficiency
    3. Cross-modal compression consistency and effectiveness
    4. Adaptive algorithms that adjust to data characteristics
    5. Novel probability prediction and arithmetic coding optimizations
    
    Key LMCompress principles to enhance:
    - Better approximation of Solomonoff distribution through improved segmentation
    - Adaptive context window management for different modalities
    - Cross-modal attention and fusion for unified compression
    - Domain-specific optimization (medical, legal, general text)
    - Differential encoding for temporal data (video, audio sequences)
    
    Advanced techniques to consider:
    - Adaptive precision arithmetic coding
    - Content-aware segmentation strategies
    - Hierarchical compression with multiple prediction scales
    - Entropy-based adaptive processing
    - Cross-modal probability sharing and consistency
    - Dynamic hyperparameter adjustment based on data characteristics
    
    Make targeted improvements to code within EVOLVE-BLOCK sections, focusing on algorithmic sophistication and performance.

  num_top_programs: 4
  num_diverse_programs: 3
  use_template_stochasticity: true
  
  template_variations:
    improvement_focus:
      - "Here's an advanced optimization for the LMCompress algorithm:"
      - "I propose these sophisticated compression improvements:"
      - "Consider these novel techniques for better multimodal compression:"
      - "Let's implement these cutting-edge LMCompress optimizations:"
    
    technical_areas:
      - "Focus on adaptive arithmetic coding precision"
      - "Optimize cross-modal probability prediction"
      - "Enhance segmentation and context management"
      - "Improve differential encoding for temporal data"
      - "Develop content-aware compression strategies"

# Database configuration for compression algorithm evolution
database:
  population_size: 60   # Moderate population for complex algorithm space
  archive_size: 20      # Keep top performing algorithms
  num_islands: 3        # Island model for diverse approaches
  elite_selection_ratio: 0.2
  exploration_ratio: 0.3  # Higher exploration for novel compression techniques
  exploitation_ratio: 0.7
  diversity_metric: "edit_distance"
  
  # Multi-objective optimization for compression metrics
  feature_dimensions:
    - "overall_score"              # Primary optimization target
    - "compression_ratio"          # Compression efficiency
    - "quality_preservation"       # Information quality
    - "processing_speed"           # Computational efficiency
    - "algorithm_sophistication"   # Algorithmic complexity
  feature_bins: 6

# Evaluator configuration for compression algorithm testing
evaluator:
  timeout: 90  # Longer timeout for complex compression processing
  max_retries: 2
  
  # Cascade evaluation for efficiency
  cascade_evaluation: true
  cascade_thresholds: [0.4, 0.7]  # Progressive quality gates
  
  # Parallel evaluation for faster iteration
  parallel_evaluations: 2
  
  # Resource limits for multimodal compression
  memory_limit_mb: 3072  # Higher memory for compression algorithms
  
  # Compression-specific evaluation features
  use_llm_feedback: false  # Deterministic evaluation for reproducibility
  
  # Performance targets based on LMCompress paper
  target_metrics:
    compression_ratio: 4.0      # 4x improvement over traditional methods
    quality_threshold: 0.75     # Minimum quality preservation
    speed_target: 2.0          # Max seconds per modality
    cross_modal_consistency: 0.8 # Cross-modal performance consistency

# Evolution settings optimized for compression algorithm development
diff_based_evolution: true
allow_full_rewrites: false  # Preserve core structure, evolve algorithms
max_code_length: 12000     # Sufficient for complex compression algorithms

# Advanced evolution parameters for compression optimization
evolution:
  mutation_rate: 0.12      # Moderate mutation for algorithmic exploration
  crossover_rate: 0.2      # Limited crossover to preserve algorithm coherence
  novelty_pressure: 0.25   # Encourage novel compression approaches
  
  # Specialized operators for compression algorithm evolution
  operators:
    - "hyperparameter_optimization"  # Compression parameter tuning
    - "algorithm_enhancement"        # Core compression logic improvements
    - "segmentation_optimization"    # Data segmentation strategies
    - "probability_prediction"       # LLM prediction improvements
    - "arithmetic_coding_tuning"     # Arithmetic coding optimizations
    - "cross_modal_fusion"          # Multimodal compression coordination

# Compression-specific optimization settings
compression_optimization:
  # Target improvements over baseline methods
  target_improvements:
    image_vs_jpeg_xl: 2.0      # 2x better than JPEG-XL
    audio_vs_flac: 2.0         # 2x better than FLAC  
    video_vs_h264: 2.0         # 2x better than H264
    text_vs_bzip2: 3.0         # 3x better than bzip2
  
  # Quality preservation requirements
  quality_requirements:
    min_image_quality: 0.8     # PSNR-based quality
    min_audio_quality: 0.75    # Perceptual quality
    min_text_semantic: 0.85    # Semantic similarity
    min_video_quality: 0.7     # Temporal consistency
  
  # Performance constraints
  performance_constraints:
    max_memory_per_modality: 1024  # MB
    max_time_per_modality: 3.0     # seconds
    min_throughput: 1.0            # MB/s

# Logging and monitoring for compression experiments
logging:
  detailed_metrics: true
  save_compression_samples: true
  track_algorithm_evolution: true
  compression_analysis: true
  
  # Compression-specific logging
  log_compression_ratios: true
  log_quality_metrics: true
  log_processing_times: true
  log_algorithm_changes: true

# Output configuration for compression optimization
output:
  save_all_generations: false
  save_top_k: 8
  include_compression_plots: true
  include_quality_analysis: true
  
  # Compression-specific outputs
  save_compression_samples: true
  generate_performance_reports: true
  track_improvement_trends: true
