{"id": "dcd3ff48-56a6-4649-aaf8-e8f7314a274b", "code": "# EVOLVE-BLOCK-START\n\"\"\"Function minimization example for OpenEvolve\"\"\"\nimport numpy as np\nimport random\n\n\ndef search_algorithm(iterations=1000, bounds=(-5, 5), num_starts=5):\n    \"\"\"\n    An improved search algorithm using simulated annealing, adaptive step size, and multiple starts.\n\n    Args:\n        iterations: Number of iterations to run\n        bounds: Bounds for the search space (min, max)\n        num_starts: Number of random starting points\n\n    Returns:\n        Tuple of (best_x, best_y, best_value)\n    \"\"\"\n    best_x, best_y, best_value = None, None, float('inf')  # Initialize with a very large value\n\n    for _ in range(num_starts):\n        # Initialize with a random point\n        x = np.random.uniform(bounds[0], bounds[1])\n        y = np.random.uniform(bounds[0], bounds[1])\n        current_value = evaluate_function(x, y)\n\n        # Simulated Annealing parameters\n        initial_temperature = 1.0\n        cooling_rate = 0.95\n        step_size = 1.0  # Initial step size\n        momentum_x, momentum_y = 0.0, 0.0\n        acceptance_rate = 0.5 # Initial acceptance rate\n        adaptive_cooling = 0.995\n\n        for i in range(iterations):\n            # Generate a new candidate solution with momentum\n            dx = random.uniform(-step_size, step_size)\n            dy = random.uniform(-step_size, step_size)\n\n            # Apply momentum\n            momentum_x = 0.9 * momentum_x + 0.1 * dx\n            momentum_y = 0.9 * momentum_y + 0.1 * dy\n            new_x = x + momentum_x\n            new_y = y + momentum_y\n\n            # Clamp the bounds\n            new_x = max(bounds[0], min(new_x, bounds[1]))\n            new_y = max(bounds[0], min(new_y, bounds[1]))\n\n            new_value = evaluate_function(new_x, new_y)\n\n            # Simulated Annealing acceptance criterion\n            delta_e = new_value - current_value\n            acceptance_probability = np.exp(-delta_e / initial_temperature)\n            if delta_e < 0 or random.random() < acceptance_probability:\n                x, y, current_value = new_x, new_y, new_value\n                acceptance_rate = 0.9 * acceptance_rate + 0.1 # Adjust acceptance rate\n            else:\n                acceptance_rate = 0.9 * acceptance_rate\n\n            # Adaptive step size (reduce with each iteration) with occasional increase\n            if random.random() < 0.01: # small probability of increasing step size\n                step_size *= 1.1\n            else:\n                step_size *= 0.99\n\n            step_size = max(0.001, min(step_size, 2.0)) # Clamp step size\n\n            # Adaptive cooling\n            initial_temperature *= adaptive_cooling\n\n            # Adjust adaptive cooling based on acceptance rate\n            if acceptance_rate > 0.5:\n                adaptive_cooling *= 0.999\n            elif acceptance_rate < 0.1:\n                adaptive_cooling *= 1.001 # Slow down cooling\n\n        if current_value < best_value:\n            best_value = current_value\n            best_x, best_y = x, y\n\n    return best_x, best_y, best_value\n\n\ndef evaluate_function(x, y):\n    \"\"\"The complex function we're trying to minimize\"\"\"\n    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20\n\n\n# EVOLVE-BLOCK-END\n\n\n# This part remains fixed (not evolved)\ndef run_search():\n    x, y, value = search_algorithm()\n    return x, y, value\n\n\nif __name__ == \"__main__\":\n    x, y, value = run_search()\n    print(f\"Found minimum at ({x}, {y}) with value {value}\")\n", "language": "python", "parent_id": "8d5035e4-d2f1-4243-b692-190a8e8befc2", "generation": 2, "timestamp": 1748200314.82416, "iteration_found": 5, "metrics": {"runs_successfully": 1.0, "value": -1.5182303088971105, "distance": 0.025870437368849978, "value_score": 0.9992309008658714, "distance_score": 0.9747819642456971, "overall_score": 1.0}, "complexity": 0.0, "diversity": 0.0, "metadata": {"changes": "Change 1: Replace 35 lines with 56 lines", "parent_metrics": {"runs_successfully": 1.0, "value": -1.4205000746119278, "distance": 3.9899641768484586, "value_score": 0.989968906606627, "distance_score": 0.7145680227794736, "overall_score": 0.2616703501595637, "speed_score": 1.0, "reliability_score": 1.0, "combined_score": 0.9083517507978184, "success_rate": 1.0}}}