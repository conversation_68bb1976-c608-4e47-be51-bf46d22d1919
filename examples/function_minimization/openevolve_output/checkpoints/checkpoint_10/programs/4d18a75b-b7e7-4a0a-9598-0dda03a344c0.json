{"id": "4d18a75b-b7e7-4a0a-9598-0dda03a344c0", "code": "# EVOLVE-BLOCK-START\n\"\"\"Function minimization example for OpenEvolve\"\"\"\nimport numpy as np\nimport random\n\n\ndef search_algorithm(iterations=1000, bounds=(-5, 5), num_starts=5):\n    \"\"\"\n    An improved search algorithm using simulated annealing, adaptive step size, and multiple starts.\n\n    Args:\n        iterations: Number of iterations to run\n        bounds: Bounds for the search space (min, max)\n        num_starts: Number of random starting points\n\n    Returns:\n        Tuple of (best_x, best_y, best_value)\n    \"\"\"\n    best_x, best_y, best_value = None, None, float('inf')  # Initialize with a very large value\n\n    for _ in range(num_starts):\n        # Initialize with a random point\n        x = np.random.uniform(bounds[0], bounds[1])\n        y = np.random.uniform(bounds[0], bounds[1])\n        current_value = evaluate_function(x, y)\n\n        # Simulated Annealing parameters\n        initial_temperature = 2.0  # Increased initial temperature for better exploration\n        cooling_rate = 0.995  # Slightly slower cooling\n        step_size = 2.0  # Initial step size, potentially larger for wider exploration\n        step_size_decay = 0.995  # step size decay rate\n        momentum_x = 0\n        momentum_y = 0\n        for i in range(iterations):\n            # Generate a new candidate solution\n            dx = random.uniform(-step_size, step_size)\n            dy = random.uniform(-step_size, step_size)\n            # Add momentum\n            momentum_x = 0.9 * momentum_x + dx\n            momentum_y = 0.9 * momentum_y + dy\n            new_x = x + momentum_x\n            new_y = y + momentum_y\n\n            # Clamp the bounds\n            new_x = max(bounds[0], min(new_x, bounds[1]))\n            new_y = max(bounds[0], min(new_y, bounds[1]))\n\n            new_value = evaluate_function(new_x, new_y)\n\n            # Simulated Annealing acceptance criterion\n            # Simulated Annealing acceptance criterion, with a small chance to accept worse solutions even at low temp\n            delta_e = new_value - current_value\n            acceptance_probability = np.exp(-delta_e / initial_temperature)\n            if delta_e < 0 or random.random() < acceptance_probability or (initial_temperature < 0.01 and random.random() < 0.001): # small chance to accept worse even at low temp\n                x, y, current_value = new_x, new_y, new_value\n            # Adaptive step size (reduce with each iteration)\n            step_size *= step_size_decay\n            # Cool down the temperature more quickly.\n            initial_temperature *= 0.99\n\n        if current_value < best_value:\n            best_value = current_value\n            best_x, best_y = x, y\n\n    return best_x, best_y, best_value\n\n\ndef evaluate_function(x, y):\n    \"\"\"The complex function we're trying to minimize\"\"\"\n    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20\n\n\n# EVOLVE-BLOCK-END\n\n\n# This part remains fixed (not evolved)\ndef run_search():\n    x, y, value = search_algorithm()\n    return x, y, value\n\n\nif __name__ == \"__main__\":\n    x, y, value = run_search()\n    print(f\"Found minimum at ({x}, {y}) with value {value}\")\n", "language": "python", "parent_id": "e47ce9f0-8074-43d7-8e93-826546e774e7", "generation": 3, "timestamp": 1748200307.917442, "iteration_found": 4, "metrics": {"runs_successfully": 1.0, "value": -1.5185784815450922, "distance": 0.0071954674239714505, "value_score": 0.9995786591480372, "distance_score": 0.992855937445415, "overall_score": 1.0}, "complexity": 0.0, "diversity": 0.0, "metadata": {"changes": "Change 1: Replace 9 lines with 13 lines\nChange 2: Replace 5 lines with 5 lines\nChange 3: Replace 10 lines with 9 lines", "parent_metrics": {"runs_successfully": 1.0, "value": -1.5178067050655903, "distance": 0.03575779228331834, "value_score": 0.9988081273212205, "distance_score": 0.9654766852349809, "overall_score": 1.0}}}