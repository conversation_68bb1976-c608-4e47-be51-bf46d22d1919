{"id": "865e01fd-19e8-450c-94d5-ac9c4dd35f6b", "code": "# EVOLVE-BLOCK-START\n\"\"\"Function minimization example for OpenEvolve\"\"\"\nimport numpy as np\n\n\ndef search_algorithm(iterations=1000, bounds=(-5, 5)):\n    \"\"\"\n    A simple random search algorithm that often gets stuck in local minima.\n\n    Args:\n        iterations: Number of iterations to run\n        bounds: Bounds for the search space (min, max)\n\n    Returns:\n        Tuple of (best_x, best_y, best_value)\n    \"\"\"\n    # Initialize with a random point\n    best_x = np.random.uniform(bounds[0], bounds[1])\n    best_y = np.random.uniform(bounds[0], bounds[1])\n    best_value = evaluate_function(best_x, best_y)\n\n    for _ in range(iterations):\n        # Simple random search\n        x = np.random.uniform(bounds[0], bounds[1])\n        y = np.random.uniform(bounds[0], bounds[1])\n        value = evaluate_function(x, y)\n\n        if value < best_value:\n            best_value = value\n            best_x, best_y = x, y\n\n    return best_x, best_y, best_value\n\n\ndef evaluate_function(x, y):\n    \"\"\"The complex function we're trying to minimize\"\"\"\n    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20\n\n\n# EVOLVE-BLOCK-END\n\n\n# This part remains fixed (not evolved)\ndef run_search():\n    x, y, value = search_algorithm()\n    return x, y, value\n\n\nif __name__ == \"__main__\":\n    x, y, value = run_search()\n    print(f\"Found minimum at ({x}, {y}) with value {value}\")\n", "language": "python", "parent_id": null, "generation": 0, "timestamp": 1748200277.890042, "iteration_found": 0, "metrics": {"runs_successfully": 1.0, "value": -1.5182859038088847, "distance": 0.01636879245798751, "value_score": 0.9992864133783733, "distance_score": 0.9838948297316358, "overall_score": 1.0}, "complexity": 0.0, "diversity": 0.0, "metadata": {}}