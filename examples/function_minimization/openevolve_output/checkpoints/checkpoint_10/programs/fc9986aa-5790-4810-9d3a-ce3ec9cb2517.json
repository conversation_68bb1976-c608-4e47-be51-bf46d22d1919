{"id": "fc9986aa-5790-4810-9d3a-ce3ec9cb2517", "code": "# EVOLVE-BLOCK-START\n\"\"\"Function minimization example for OpenEvolve\"\"\"\nimport numpy as np\nimport random\n\n\ndef search_algorithm(iterations=1000, bounds=(-5, 5), num_starts=5):\n    \"\"\"\n    An improved search algorithm using simulated annealing, adaptive step size, and multiple starts.\n\n    Args:\n        iterations: Number of iterations to run\n        bounds: Bounds for the search space (min, max)\n        num_starts: Number of random starting points\n\n    Returns:\n        Tuple of (best_x, best_y, best_value)\n    \"\"\"\n    best_x, best_y, best_value = None, None, float('inf')  # Initialize with a very large value\n    rng = np.random.default_rng() # Use a random number generator for consistency\n\n    for _ in range(num_starts):\n        # Initialize with a random point\n        x = rng.uniform(bounds[0], bounds[1])\n        y = rng.uniform(bounds[0], bounds[1])\n        current_value = evaluate_function(x, y)\n\n        # Simulated Annealing parameters - tune these\n        initial_temperature = 2.0  # Increased initial temperature\n        cooling_rate = 0.97  # Slightly slower cooling\n        step_size = 1.5  # Initial step size\n        step_size_reduction = 0.98  # Step size reduction factor\n\n        for i in range(iterations):\n            # Generate a new candidate solution - Cauchy distribution for heavier tails\n            dx = rng.standard_cauchy() * step_size # Use cauchy distribution\n            dy = rng.standard_cauchy() * step_size\n            new_x = x + dx\n            new_y = y + dy\n\n            # Clamp the bounds\n            new_x = np.clip(new_x, bounds[0], bounds[1])\n            new_y = np.clip(new_y, bounds[0], bounds[1])\n\n            new_value = evaluate_function(new_x, new_y)\n\n            # Simulated Annealing acceptance criterion\n            delta_e = new_value - current_value\n            acceptance_probability = np.exp(-delta_e / initial_temperature)\n            if delta_e < 0 or rng.random() < acceptance_probability:\n                x, y, current_value = new_x, new_y, new_value\n\n            # Adaptive step size (reduce with each iteration)\n            # Adaptive step size (reduce with each iteration)\n            step_size *= step_size_reduction\n\n            # Cool down the temperature - consider a dynamic schedule\n            initial_temperature *= cooling_rate\n\n\n        if current_value < best_value:\n            best_value = current_value\n            best_x, best_y = x, y\n\n    return best_x, best_y, best_value\n\n\ndef evaluate_function(x, y):\n    \"\"\"The complex function we're trying to minimize\"\"\"\n    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20\n\n\n# EVOLVE-BLOCK-END\n\n\n# This part remains fixed (not evolved)\ndef run_search():\n    x, y, value = search_algorithm()\n    return x, y, value\n\n\nif __name__ == \"__main__\":\n    x, y, value = run_search()\n    print(f\"Found minimum at ({x}, {y}) with value {value}\")\n", "language": "python", "parent_id": "f73eadc7-fa03-4fdb-8701-fd4cdb86bc64", "generation": 3, "timestamp": 1748200334.573832, "iteration_found": 8, "metrics": {"runs_successfully": 1.0, "value": -1.5186858407763189, "distance": 0.0005063033748935245, "value_score": 0.9996859394413402, "distance_score": 0.9994939528384922, "overall_score": 1.0}, "complexity": 0.0, "diversity": 0.0, "metadata": {"changes": "Change 1: Replace 5 lines with 5 lines\nChange 2: Replace 5 lines with 5 lines\nChange 3: Replace 4 lines with 5 lines", "parent_metrics": {"runs_successfully": 1.0, "value": -1.5186858408359631, "distance": 0.0004974157189533313, "value_score": 0.9996859395009471, "distance_score": 0.9995028315804336, "overall_score": 1.0}}}