{"id": "cbf807e8-e743-4183-aa96-c5e466c4af7e", "code": "# EVOLVE-BLOCK-START\n\"\"\"Function minimization example for OpenEvolve\"\"\"\nimport numpy as np\nimport random\n\n\ndef search_algorithm(iterations=1000, bounds=(-5, 5), num_starts=5):\n    \"\"\"\n    An improved search algorithm using simulated annealing, adaptive step size, and multiple starts.\n\n    Args:\n        iterations: Number of iterations to run\n        bounds: Bounds for the search space (min, max)\n        num_starts: Number of times to restart the search\n\n    Returns:\n        Tuple of (best_x, best_y, best_value)\n    \"\"\"\n    best_x, best_y, best_value = None, None, float('inf')  # Initialize with positive infinity\n\n    for _ in range(num_starts):\n        # Initialize with a random point\n        x = np.random.uniform(bounds[0], bounds[1])\n        y = np.random.uniform(bounds[0], bounds[1])\n        current_value = evaluate_function(x, y)\n        current_x, current_y = x, y\n\n        # Simulated Annealing parameters\n        initial_temp = 1.0\n        cooling_rate = 0.995\n\n        for iteration in range(iterations):\n            # Adaptive step size (scaled down by the iteration number to converge)\n            step_size = (bounds[1] - bounds[0]) / (10 + iteration/10)\n            # Randomly move in x and y directions.\n            dx = random.uniform(-step_size, step_size)\n            dy = random.uniform(-step_size, step_size)\n\n            # Propose a new solution\n            new_x = current_x + dx\n            new_y = current_y + dy\n\n            # Ensure the new point is within the bounds\n            new_x = np.clip(new_x, bounds[0], bounds[1])\n            new_y = np.clip(new_y, bounds[0], bounds[1])\n\n            new_value = evaluate_function(new_x, new_y)\n\n            # Simulated Annealing acceptance criterion\n            temp = initial_temp * (cooling_rate ** iteration)\n            delta_e = new_value - current_value\n            if delta_e < 0 or random.random() < np.exp(-delta_e / temp):\n                current_x, current_y = new_x, new_y\n                current_value = new_value\n\n        # Update best solution found so far\n        if current_value < best_value:\n            best_value = current_value\n            best_x, best_y = current_x, current_y\n\n    return best_x, best_y, best_value\n\n\ndef evaluate_function(x, y):\n    \"\"\"The complex function we're trying to minimize\"\"\"\n    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20\n\n\n# EVOLVE-BLOCK-END\n\n\n# This part remains fixed (not evolved)\ndef run_search():\n    x, y, value = search_algorithm()\n    return x, y, value\n\n\nif __name__ == \"__main__\":\n    x, y, value = run_search()\n    print(f\"Found minimum at ({x}, {y}) with value {value}\")\n", "language": "python", "parent_id": "865e01fd-19e8-450c-94d5-ac9c4dd35f6b", "generation": 1, "timestamp": 1748200349.733307, "iteration_found": 10, "metrics": {"runs_successfully": 1.0, "value": -1.5177988155037574, "distance": 0.025415017881062915, "value_score": 0.9988002566169087, "distance_score": 0.9752148959807699, "overall_score": 1.0}, "complexity": 0.0, "diversity": 0.0, "metadata": {"changes": "Change 1: Replace 35 lines with 64 lines", "parent_metrics": {"runs_successfully": 1.0, "value": -1.5182859038088847, "distance": 0.01636879245798751, "value_score": 0.9992864133783733, "distance_score": 0.9838948297316358, "overall_score": 1.0}}}