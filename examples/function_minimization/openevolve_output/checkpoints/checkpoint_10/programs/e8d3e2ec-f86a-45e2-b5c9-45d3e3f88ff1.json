{"id": "e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1", "code": "# EVOLVE-BLOCK-START\n\"\"\"Function minimization example for OpenEvolve\"\"\"\nimport numpy as np\nimport random\n\n\ndef search_algorithm(iterations=1000, bounds=(-5, 5), num_starts=5):\n    \"\"\"\n    An improved search algorithm using simulated annealing, adaptive step size, and multiple starts.\n\n    Args:\n        iterations: Number of iterations to run per start.\n        bounds: Bounds for the search space (min, max).\n        num_starts: Number of random starting points.\n\n    Returns:\n        Tuple of (best_x, best_y, best_value)\n    \"\"\"\n    best_x, best_y, best_value = None, None, float('inf')\n\n    for _ in range(num_starts):\n        # Initialize with a random point\n        x = np.random.uniform(bounds[0], bounds[1])\n        y = np.random.uniform(bounds[0], bounds[1])\n        current_value = evaluate_function(x, y)\n\n        # Simulated Annealing parameters\n        initial_temperature = 1.0\n        cooling_rate = 0.995  # Adjust for faster/slower cooling\n        step_size = 0.5  # Initial step size\n        step_size_increase = 1.1\n        step_size_decrease = 0.5\n\n\n        for i in range(iterations):\n            # Generate a new candidate solution\n            dx = np.random.uniform(-step_size, step_size)\n            dy = np.random.uniform(-step_size, step_size)\n            new_x = x + dx\n            new_y = y + dy\n\n            # Clip to bounds\n            new_x = np.clip(new_x, bounds[0], bounds[1])\n            new_y = np.clip(new_y, bounds[0], bounds[1])\n\n            new_value = evaluate_function(new_x, new_y)\n\n            # Simulated Annealing acceptance criterion\n            delta_energy = new_value - current_value\n            if delta_energy < 0 or np.random.rand() < np.exp(-delta_energy / initial_temperature * (cooling_rate**i)):\n                x, y = new_x, new_y\n                current_value = new_value\n                if delta_energy < 0:\n                    step_size *= step_size_increase # Increase step size if move improved the value\n                else:\n                    step_size *= step_size_decrease # Reduce step size if move worsened the value\n            else:\n                step_size *= step_size_decrease # Reduce step size if move was rejected\n\n            step_size = np.clip(step_size, 1e-6, 2) # clamp the step size to be within certain range\n\n        # Update the global best\n        if current_value < best_value:\n            best_value = current_value\n            best_x, best_y = x, y\n\n    return best_x, best_y, best_value\n\n\ndef evaluate_function(x, y):\n    \"\"\"The complex function we're trying to minimize\"\"\"\n    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20\n\n\n# EVOLVE-BLOCK-END\n\n\n# This part remains fixed (not evolved)\ndef run_search():\n    x, y, value = search_algorithm()\n    return x, y, value\n\n\nif __name__ == \"__main__\":\n    x, y, value = run_search()\n    print(f\"Found minimum at ({x}, {y}) with value {value}\")\n", "language": "python", "parent_id": "865e01fd-19e8-450c-94d5-ac9c4dd35f6b", "generation": 1, "timestamp": 1748200289.2446349, "iteration_found": 1, "metrics": {"runs_successfully": 1.0, "value": 0.10197369033683856, "distance": 2.344123661278931, "value_score": 0.45112532860118154, "distance_score": 0.2350279732277658, "overall_score": 0.16823671782580774, "speed_score": 1.0, "reliability_score": 1.0, "combined_score": 0.44118358912903866, "success_rate": 1.0}, "complexity": 0.0, "diversity": 0.0, "metadata": {"changes": "Change 1: Replace 49 lines with 84 lines", "parent_metrics": {"runs_successfully": 1.0, "value": -1.5182859038088847, "distance": 0.01636879245798751, "value_score": 0.9992864133783733, "distance_score": 0.9838948297316358, "overall_score": 1.0}}}