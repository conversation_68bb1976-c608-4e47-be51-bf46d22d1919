2025-05-25 23:11:17,812 - openevolve.controller - INFO - Logging to openevolve_output/logs/openevolve_20250525_231117.log
2025-05-25 23:11:17,847 - openevolve.llm.openai - INFO - Initialized OpenAI LLM with model: gemini-2.0-flash-lite
2025-05-25 23:11:17,855 - openevolve.llm.openai - INFO - Initialized OpenAI LLM with model: gemini-2.0-flash
2025-05-25 23:11:17,855 - openevolve.llm.ensemble - INFO - Initialized LLM ensemble with models: gemini-2.0-flash-lite (weight: 0.80), gemini-2.0-flash (weight: 0.20)
2025-05-25 23:11:17,856 - openevolve.prompt.sampler - INFO - Initialized prompt sampler
2025-05-25 23:11:17,856 - openevolve.database - INFO - Initialized program database with 0 programs
2025-05-25 23:11:17,858 - openevolve.evaluator - INFO - Successfully loaded evaluation function from evaluator.py
2025-05-25 23:11:17,858 - openevolve.evaluator - INFO - Initialized evaluator with evaluator.py
2025-05-25 23:11:17,858 - openevolve.controller - INFO - Initialized OpenEvolve with initial_program.py and evaluator.py
2025-05-25 23:11:17,859 - openevolve.controller - INFO - Adding initial program to database
2025-05-25 23:11:17,889 - openevolve.evaluator - INFO - Evaluated program 865e01fd-19e8-450c-94d5-ac9c4dd35f6b in 0.03s: runs_successfully=1.0000, value=-1.5183, distance=0.0164, value_score=0.9993, distance_score=0.9839, overall_score=1.0000
2025-05-25 23:11:17,890 - openevolve.controller - INFO - Starting evolution from iteration 0 for 10 iterations (total: 10)
2025-05-25 23:11:28,419 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:11:29,243 - openevolve.evaluator - INFO - Evaluated program e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1 in 0.81s: runs_successfully=1.0000, value=0.1020, distance=2.3441, value_score=0.4511, distance_score=0.2350, overall_score=0.1682, speed_score=1.0000, reliability_score=1.0000, combined_score=0.4412, success_rate=1.0000
2025-05-25 23:11:29,244 - openevolve.database - INFO - New best program e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1 replaces 865e01fd-19e8-450c-94d5-ac9c4dd35f6b
2025-05-25 23:11:29,244 - openevolve.controller - INFO - Iteration 1: Child e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1 from parent 865e01fd-19e8-450c-94d5-ac9c4dd35f6b in 11.35s. Metrics: runs_successfully=1.0000, value=0.1020, distance=2.3441, value_score=0.4511, distance_score=0.2350, overall_score=0.1682, speed_score=1.0000, reliability_score=1.0000, combined_score=0.4412, success_rate=1.0000 (Δ: runs_successfully=+0.0000, value=+1.6203, distance=+2.3278, value_score=-0.5482, distance_score=-0.7489, overall_score=-0.8318)
2025-05-25 23:11:29,244 - openevolve.controller - INFO - 🌟 New best solution found at iteration 1: e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1
2025-05-25 23:11:29,244 - openevolve.controller - INFO - Metrics: runs_successfully=1.0000, value=0.1020, distance=2.3441, value_score=0.4511, distance_score=0.2350, overall_score=0.1682, speed_score=1.0000, reliability_score=1.0000, combined_score=0.4412, success_rate=1.0000
2025-05-25 23:11:35,965 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:11:36,156 - openevolve.evaluator - INFO - Evaluated program 8d5035e4-d2f1-4243-b692-190a8e8befc2 in 0.19s: runs_successfully=1.0000, value=-1.4205, distance=3.9900, value_score=0.9900, distance_score=0.7146, overall_score=0.2617, speed_score=1.0000, reliability_score=1.0000, combined_score=0.9084, success_rate=1.0000
2025-05-25 23:11:36,156 - openevolve.database - INFO - New best program 8d5035e4-d2f1-4243-b692-190a8e8befc2 replaces e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1 (combined_score: 0.4412 → 0.9084, +0.4672)
2025-05-25 23:11:36,156 - openevolve.controller - INFO - Iteration 2: Child 8d5035e4-d2f1-4243-b692-190a8e8befc2 from parent 865e01fd-19e8-450c-94d5-ac9c4dd35f6b in 6.91s. Metrics: runs_successfully=1.0000, value=-1.4205, distance=3.9900, value_score=0.9900, distance_score=0.7146, overall_score=0.2617, speed_score=1.0000, reliability_score=1.0000, combined_score=0.9084, success_rate=1.0000 (Δ: runs_successfully=+0.0000, value=+0.0978, distance=+3.9736, value_score=-0.0093, distance_score=-0.2693, overall_score=-0.7383)
2025-05-25 23:11:36,156 - openevolve.controller - INFO - 🌟 New best solution found at iteration 2: 8d5035e4-d2f1-4243-b692-190a8e8befc2
2025-05-25 23:11:36,156 - openevolve.controller - INFO - Metrics: runs_successfully=1.0000, value=-1.4205, distance=3.9900, value_score=0.9900, distance_score=0.7146, overall_score=0.2617, speed_score=1.0000, reliability_score=1.0000, combined_score=0.9084, success_rate=1.0000
2025-05-25 23:11:42,213 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:11:42,236 - openevolve.evaluator - INFO - Evaluated program e47ce9f0-8074-43d7-8e93-826546e774e7 in 0.02s: runs_successfully=1.0000, value=-1.5178, distance=0.0358, value_score=0.9988, distance_score=0.9655, overall_score=1.0000
2025-05-25 23:11:42,236 - openevolve.controller - INFO - Iteration 3: Child e47ce9f0-8074-43d7-8e93-826546e774e7 from parent 8d5035e4-d2f1-4243-b692-190a8e8befc2 in 6.08s. Metrics: runs_successfully=1.0000, value=-1.5178, distance=0.0358, value_score=0.9988, distance_score=0.9655, overall_score=1.0000 (Δ: runs_successfully=+0.0000, value=-0.0973, distance=-3.9542, value_score=+0.0088, distance_score=+0.2509, overall_score=+0.7383)
2025-05-25 23:11:47,890 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:11:47,917 - openevolve.evaluator - INFO - Evaluated program 4d18a75b-b7e7-4a0a-9598-0dda03a344c0 in 0.02s: runs_successfully=1.0000, value=-1.5186, distance=0.0072, value_score=0.9996, distance_score=0.9929, overall_score=1.0000
2025-05-25 23:11:47,917 - openevolve.controller - INFO - Iteration 4: Child 4d18a75b-b7e7-4a0a-9598-0dda03a344c0 from parent e47ce9f0-8074-43d7-8e93-826546e774e7 in 5.68s. Metrics: runs_successfully=1.0000, value=-1.5186, distance=0.0072, value_score=0.9996, distance_score=0.9929, overall_score=1.0000 (Δ: runs_successfully=+0.0000, value=-0.0008, distance=-0.0286, value_score=+0.0008, distance_score=+0.0274, overall_score=+0.0000)
2025-05-25 23:11:54,793 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:11:54,823 - openevolve.evaluator - INFO - Evaluated program dcd3ff48-56a6-4649-aaf8-e8f7314a274b in 0.03s: runs_successfully=1.0000, value=-1.5182, distance=0.0259, value_score=0.9992, distance_score=0.9748, overall_score=1.0000
2025-05-25 23:11:54,824 - openevolve.controller - INFO - Iteration 5: Child dcd3ff48-56a6-4649-aaf8-e8f7314a274b from parent 8d5035e4-d2f1-4243-b692-190a8e8befc2 in 6.91s. Metrics: runs_successfully=1.0000, value=-1.5182, distance=0.0259, value_score=0.9992, distance_score=0.9748, overall_score=1.0000 (Δ: runs_successfully=+0.0000, value=-0.0977, distance=-3.9641, value_score=+0.0093, distance_score=+0.2602, overall_score=+0.7383)
2025-05-25 23:11:54,825 - openevolve.database - INFO - Saved database with 6 programs to openevolve_output/checkpoints/checkpoint_5
2025-05-25 23:11:54,826 - openevolve.controller - INFO - Saved best program at checkpoint 5 with metrics: runs_successfully=1.0000, value=-1.4205, distance=3.9900, value_score=0.9900, distance_score=0.7146, overall_score=0.2617, speed_score=1.0000, reliability_score=1.0000, combined_score=0.9084, success_rate=1.0000
2025-05-25 23:11:54,826 - openevolve.controller - INFO - Saved checkpoint at iteration 5 to openevolve_output/checkpoints/checkpoint_5
2025-05-25 23:12:03,023 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:12:03,088 - openevolve.evaluator - INFO - Evaluated program f73eadc7-fa03-4fdb-8701-fd4cdb86bc64 in 0.06s: runs_successfully=1.0000, value=-1.5187, distance=0.0005, value_score=0.9997, distance_score=0.9995, overall_score=1.0000
2025-05-25 23:12:03,088 - openevolve.controller - INFO - Iteration 6: Child f73eadc7-fa03-4fdb-8701-fd4cdb86bc64 from parent 8d5035e4-d2f1-4243-b692-190a8e8befc2 in 8.26s. Metrics: runs_successfully=1.0000, value=-1.5187, distance=0.0005, value_score=0.9997, distance_score=0.9995, overall_score=1.0000 (Δ: runs_successfully=+0.0000, value=-0.0982, distance=-3.9895, value_score=+0.0097, distance_score=+0.2849, overall_score=+0.7383)
2025-05-25 23:12:08,283 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:12:08,288 - openevolve.evaluator - ERROR - Error evaluating program: Unknown format code 'f' for object of type 'str'
2025-05-25 23:12:08,289 - openevolve.controller - INFO - Iteration 7: Child ff4cfd65-6637-4624-9e22-db3dd408c4c7 from parent f73eadc7-fa03-4fdb-8701-fd4cdb86bc64 in 5.20s. Metrics: error=0.0000 (Δ: )
2025-05-25 23:12:14,518 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:12:14,573 - openevolve.evaluator - INFO - Evaluated program fc9986aa-5790-4810-9d3a-ce3ec9cb2517 in 0.05s: runs_successfully=1.0000, value=-1.5187, distance=0.0005, value_score=0.9997, distance_score=0.9995, overall_score=1.0000
2025-05-25 23:12:14,573 - openevolve.controller - INFO - Iteration 8: Child fc9986aa-5790-4810-9d3a-ce3ec9cb2517 from parent f73eadc7-fa03-4fdb-8701-fd4cdb86bc64 in 6.28s. Metrics: runs_successfully=1.0000, value=-1.5187, distance=0.0005, value_score=0.9997, distance_score=0.9995, overall_score=1.0000 (Δ: runs_successfully=+0.0000, value=+0.0000, distance=+0.0000, value_score=-0.0000, distance_score=-0.0000, overall_score=+0.0000)
2025-05-25 23:12:21,437 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:12:22,234 - openevolve.evaluator - INFO - Evaluated program cbf42d0e-cc0d-47a1-9847-f483311ea441 in 0.79s: runs_successfully=1.0000, value=-1.1553, distance=3.9672, value_score=0.4716, distance_score=0.2327, overall_score=0.1706, speed_score=1.0000, reliability_score=1.0000, combined_score=0.4528, success_rate=1.0000
2025-05-25 23:12:22,234 - openevolve.controller - INFO - Iteration 9: Child cbf42d0e-cc0d-47a1-9847-f483311ea441 from parent e8d3e2ec-f86a-45e2-b5c9-45d3e3f88ff1 in 7.66s. Metrics: runs_successfully=1.0000, value=-1.1553, distance=3.9672, value_score=0.4716, distance_score=0.2327, overall_score=0.1706, speed_score=1.0000, reliability_score=1.0000, combined_score=0.4528, success_rate=1.0000 (Δ: runs_successfully=+0.0000, value=-1.2573, distance=+1.6231, value_score=+0.0204, distance_score=-0.0023, overall_score=+0.0023, speed_score=+0.0000, reliability_score=+0.0000, combined_score=+0.0116, success_rate=+0.0000)
2025-05-25 23:12:29,670 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions "HTTP/1.1 200 OK"
2025-05-25 23:12:29,733 - openevolve.evaluator - INFO - Evaluated program cbf807e8-e743-4183-aa96-c5e466c4af7e in 0.06s: runs_successfully=1.0000, value=-1.5178, distance=0.0254, value_score=0.9988, distance_score=0.9752, overall_score=1.0000
2025-05-25 23:12:29,733 - openevolve.controller - INFO - Iteration 10: Child cbf807e8-e743-4183-aa96-c5e466c4af7e from parent 865e01fd-19e8-450c-94d5-ac9c4dd35f6b in 7.50s. Metrics: runs_successfully=1.0000, value=-1.5178, distance=0.0254, value_score=0.9988, distance_score=0.9752, overall_score=1.0000 (Δ: runs_successfully=+0.0000, value=+0.0005, distance=+0.0090, value_score=-0.0005, distance_score=-0.0087, overall_score=+0.0000)
2025-05-25 23:12:29,736 - openevolve.database - INFO - Saved database with 11 programs to openevolve_output/checkpoints/checkpoint_10
2025-05-25 23:12:29,736 - openevolve.controller - INFO - Saved best program at checkpoint 10 with metrics: runs_successfully=1.0000, value=-1.4205, distance=3.9900, value_score=0.9900, distance_score=0.7146, overall_score=0.2617, speed_score=1.0000, reliability_score=1.0000, combined_score=0.9084, success_rate=1.0000
2025-05-25 23:12:29,736 - openevolve.controller - INFO - Saved checkpoint at iteration 10 to openevolve_output/checkpoints/checkpoint_10
2025-05-25 23:12:29,736 - openevolve.controller - INFO - Using tracked best program: 8d5035e4-d2f1-4243-b692-190a8e8befc2
2025-05-25 23:12:29,736 - openevolve.controller - INFO - Evolution complete. Best program has metrics: runs_successfully=1.0000, value=-1.4205, distance=3.9900, value_score=0.9900, distance_score=0.7146, overall_score=0.2617, speed_score=1.0000, reliability_score=1.0000, combined_score=0.9084, success_rate=1.0000
2025-05-25 23:12:29,736 - openevolve.controller - INFO - Saved best program to openevolve_output/best/best_program.py with program info to openevolve_output/best/best_program_info.json
