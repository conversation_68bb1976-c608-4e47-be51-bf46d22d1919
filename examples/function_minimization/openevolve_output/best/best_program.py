# EVOLVE-BLOCK-START
"""Function minimization example for OpenEvolve"""
import numpy as np
import random


def search_algorithm(iterations=1000, bounds=(-5, 5), num_starts=5):
    """
    An improved search algorithm using simulated annealing, adaptive step size, and multiple starts.

    Args:
        iterations: Number of iterations to run
        bounds: Bounds for the search space (min, max)
        num_starts: Number of random starting points

    Returns:
        Tuple of (best_x, best_y, best_value)
    """
    best_x, best_y, best_value = None, None, float('inf')  # Initialize with a very large value

    for _ in range(num_starts):
        # Initialize with a random point
        x = np.random.uniform(bounds[0], bounds[1])
        y = np.random.uniform(bounds[0], bounds[1])
        current_value = evaluate_function(x, y)

        # Simulated Annealing parameters
        initial_temperature = 1.0
        cooling_rate = 0.95
        step_size = 1.0  # Initial step size

        for i in range(iterations):
            # Generate a new candidate solution
            dx = random.uniform(-step_size, step_size)
            dy = random.uniform(-step_size, step_size)
            new_x = x + dx
            new_y = y + dy

            # Clamp the bounds
            new_x = max(bounds[0], min(new_x, bounds[1]))
            new_y = max(bounds[0], min(new_y, bounds[1]))

            new_value = evaluate_function(new_x, new_y)

            # Simulated Annealing acceptance criterion
            delta_e = new_value - current_value
            if delta_e < 0 or random.random() < np.exp(-delta_e / initial_temperature):
                x, y, current_value = new_x, new_y, new_value

            # Adaptive step size (reduce with each iteration)
            step_size *= 0.99

            # Cool down the temperature
            initial_temperature *= cooling_rate


        if current_value < best_value:
            best_value = current_value
            best_x, best_y = x, y

    return best_x, best_y, best_value


def evaluate_function(x, y):
    """The complex function we're trying to minimize"""
    return np.sin(x) * np.cos(y) + np.sin(x * y) + (x**2 + y**2) / 20


# EVOLVE-BLOCK-END


# This part remains fixed (not evolved)
def run_search():
    x, y, value = search_algorithm()
    return x, y, value


if __name__ == "__main__":
    x, y, value = run_search()
    print(f"Found minimum at ({x}, {y}) with value {value}")
