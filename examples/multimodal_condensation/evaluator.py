"""
Evaluator for Multimodal Data Condensation Algorithm
Measures compression efficiency, information preservation, and processing speed
"""

import importlib.util
import numpy as np
import time
import concurrent.futures
import traceback
import json
from typing import Dict, List, Any, Tuple
import hashlib

def run_with_timeout(func, args=(), kwargs={}, timeout_seconds=30):
    """Run a function with timeout protection."""
    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
        future = executor.submit(func, *args, **kwargs)
        try:
            return future.result(timeout=timeout_seconds)
        except concurrent.futures.TimeoutError:
            raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds} seconds")

def generate_test_dataset(num_samples=20):
    """Generate synthetic multimodal test dataset."""
    dataset = []
    
    for i in range(num_samples):
        # Create correlated multimodal data
        theme_seed = i * 42
        np.random.seed(theme_seed)
        
        # Text data with varying complexity
        text_templates = [
            "The red car drives through the busy city street with music playing loudly.",
            "A person walks in the park while birds sing and leaves rustle in the wind.",
            "The chef prepares a delicious meal as kitchen sounds fill the air.",
            "Children play on the playground with laughter and excitement.",
            "The ocean waves crash against the shore under a bright blue sky."
        ]
        text = text_templates[i % len(text_templates)] + f" Sample {i} with additional context."
        
        # Image data (simulated with patterns)
        image = np.random.rand(64, 64, 3) * 255
        # Add some structure based on theme
        if "red" in text:
            image[:, :, 0] += 50  # More red
        if "blue" in text:
            image[:, :, 2] += 50  # More blue
        
        # Video data (sequence of related images)
        video = []
        for frame_idx in range(8):
            frame = image.copy() + np.random.normal(0, 10, image.shape)
            frame = np.clip(frame, 0, 255)
            video.append(frame)
        
        # Audio data (simulated with frequency patterns)
        audio_length = 4000 + np.random.randint(-500, 500)
        audio = np.random.normal(0, 0.3, audio_length)
        # Add theme-based frequency content
        if "music" in text:
            # Add musical tones
            t = np.linspace(0, 2, audio_length)
            audio += 0.2 * np.sin(2 * np.pi * 440 * t)  # A note
        if "birds" in text:
            # Add high frequency chirps
            t = np.linspace(0, 2, audio_length)
            audio += 0.1 * np.sin(2 * np.pi * 2000 * t) * np.random.rand(audio_length)
        
        dataset.append({
            'text': text,
            'image': image,
            'video': video,
            'audio': audio,
            'theme_id': i % 5,  # For cross-modal evaluation
            'sample_id': i
        })
    
    return dataset

def calculate_input_size(data_item):
    """Calculate total input size in bytes."""
    size = 0
    if data_item.get('text'):
        size += len(data_item['text'].encode('utf-8'))
    if data_item.get('image') is not None:
        size += data_item['image'].nbytes
    if data_item.get('video'):
        size += sum(frame.nbytes for frame in data_item['video'])
    if data_item.get('audio') is not None:
        size += data_item['audio'].nbytes
    return size

def calculate_semantic_preservation(original_data, condensed_repr):
    """
    Estimate semantic preservation using deterministic similarity measures.
    In a real implementation, this would use pre-trained models like CLIP.
    """
    # Create deterministic "semantic" features from original data
    semantic_features = []
    
    # Text semantics (word diversity, length, complexity)
    if original_data.get('text'):
        text = original_data['text']
        words = text.lower().split()
        semantic_features.extend([
            len(set(words)) / max(len(words), 1),  # Word diversity
            min(len(text) / 100, 1.0),  # Length factor
            sum(1 for w in words if len(w) > 6) / max(len(words), 1)  # Complexity
        ])
    
    # Image semantics (color diversity, contrast, structure)
    if original_data.get('image') is not None:
        img = original_data['image']
        semantic_features.extend([
            np.std(img) / 255.0,  # Contrast
            len(np.unique(img.astype(int))) / (256**3),  # Color diversity
            np.mean(np.abs(np.diff(img, axis=0))) / 255.0  # Edge content
        ])
    
    # Video semantics (motion, temporal consistency)
    if original_data.get('video'):
        video = original_data['video']
        if len(video) > 1:
            motion = np.mean([np.mean(np.abs(video[i] - video[i-1])) for i in range(1, len(video))])
            semantic_features.append(motion / 255.0)
        else:
            semantic_features.append(0.0)
    
    # Audio semantics (energy, frequency content)
    if original_data.get('audio') is not None:
        audio = original_data['audio']
        semantic_features.extend([
            np.std(audio),  # Energy variation
            np.mean(np.abs(audio)),  # Average energy
        ])
    
    # Pad to consistent length
    while len(semantic_features) < 10:
        semantic_features.append(0.0)
    
    original_semantic = np.array(semantic_features[:10])
    
    # Compare with condensed representation
    condensed_norm = condensed_repr / (np.linalg.norm(condensed_repr) + 1e-8)
    original_norm = original_semantic / (np.linalg.norm(original_semantic) + 1e-8)
    
    # Use first N dimensions of condensed representation
    comparison_dim = min(len(condensed_norm), len(original_norm))
    similarity = np.dot(condensed_norm[:comparison_dim], original_norm[:comparison_dim])
    
    return max(0.0, min(1.0, (similarity + 1) / 2))  # Normalize to [0,1]

def calculate_cross_modal_preservation(original_data, condensed_repr):
    """
    Estimate how well cross-modal relationships are preserved.
    """
    cross_modal_score = 0.0
    num_tests = 0
    
    # Test 1: Text-Image correlation
    if original_data.get('text') and original_data.get('image') is not None:
        text = original_data['text'].lower()
        image = original_data['image']
        
        # Check color-word correlation
        if 'red' in text:
            red_intensity = np.mean(image[:, :, 0]) / 255.0
            cross_modal_score += red_intensity
        elif 'blue' in text:
            blue_intensity = np.mean(image[:, :, 2]) / 255.0
            cross_modal_score += blue_intensity
        else:
            cross_modal_score += 0.5  # Neutral score
        num_tests += 1
    
    # Test 2: Text-Audio correlation  
    if original_data.get('text') and original_data.get('audio') is not None:
        text = original_data['text'].lower()
        audio = original_data['audio']
        
        if 'music' in text:
            # Check for tonal content (simplified)
            fft = np.abs(np.fft.fft(audio[:1000]))
            peak_freq = np.argmax(fft[:500])
            if 200 < peak_freq < 600:  # Musical range
                cross_modal_score += 0.8
            else:
                cross_modal_score += 0.3
        else:
            cross_modal_score += 0.5
        num_tests += 1
    
    # Test 3: Video-Audio synchronization
    if original_data.get('video') and original_data.get('audio') is not None:
        # Simple temporal alignment check
        video_changes = len(original_data['video'])
        audio_segments = len(original_data['audio']) // 1000
        sync_score = 1.0 - abs(video_changes - audio_segments) / max(video_changes, audio_segments, 1)
        cross_modal_score += max(0.0, sync_score)
        num_tests += 1
    
    return cross_modal_score / max(num_tests, 1)

def calculate_information_density(condensed_repr):
    """Calculate information density of the condensed representation."""
    if len(condensed_repr) == 0:
        return 0.0
    
    # Measure entropy/information content
    # Higher variance and non-zero elements indicate more information
    non_zero_ratio = np.count_nonzero(condensed_repr) / len(condensed_repr)
    variance_score = min(np.var(condensed_repr), 1.0)
    
    return (non_zero_ratio + variance_score) / 2.0

def evaluate(program_path):
    """
    Main evaluation function for the multimodal condensation algorithm.
    """
    try:
        # Load the program
        spec = importlib.util.spec_from_file_location("program", program_path)
        program = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(program)
        
        # Check required function exists
        if not hasattr(program, "process_multimodal_data"):
            return {
                "compression_ratio": 0.0,
                "semantic_preservation": 0.0,
                "cross_modal_preservation": 0.0,
                "processing_speed": 0.0,
                "information_density": 0.0,
                "overall_score": 0.0,
                "error": "Missing process_multimodal_data function"
            }
        
        # Generate test dataset
        test_dataset = generate_test_dataset(15)
        
        # Evaluation metrics
        compression_ratios = []
        semantic_scores = []
        cross_modal_scores = []
        processing_times = []
        density_scores = []
        success_count = 0
        
        for i, data_item in enumerate(test_dataset):
            try:
                # Measure input size
                input_size = calculate_input_size(data_item)
                
                # Process with timeout
                start_time = time.time()
                result = run_with_timeout(
                    program.process_multimodal_data,
                    kwargs={
                        'text': data_item.get('text'),
                        'image': data_item.get('image'),
                        'video': data_item.get('video'),
                        'audio': data_item.get('audio')
                    },
                    timeout_seconds=15
                )
                end_time = time.time()
                
                # Validate result
                if not isinstance(result, np.ndarray) or result.size == 0:
                    continue
                
                # Calculate metrics
                output_size = result.nbytes
                compression_ratio = input_size / max(output_size, 1)
                
                semantic_score = calculate_semantic_preservation(data_item, result)
                cross_modal_score = calculate_cross_modal_preservation(data_item, result)
                density_score = calculate_information_density(result)
                processing_time = end_time - start_time
                
                # Store metrics
                compression_ratios.append(compression_ratio)
                semantic_scores.append(semantic_score)
                cross_modal_scores.append(cross_modal_score)
                density_scores.append(density_score)
                processing_times.append(processing_time)
                success_count += 1
                
            except Exception as e:
                print(f"Error processing sample {i}: {str(e)}")
                continue
        
        # Calculate final scores
        if success_count == 0:
            return {
                "compression_ratio": 0.0,
                "semantic_preservation": 0.0,
                "cross_modal_preservation": 0.0,
                "processing_speed": 0.0,
                "information_density": 0.0,
                "overall_score": 0.0,
                "success_rate": 0.0,
                "error": "All samples failed"
            }
        
        avg_compression = np.mean(compression_ratios)
        avg_semantic = np.mean(semantic_scores)
        avg_cross_modal = np.mean(cross_modal_scores)
        avg_density = np.mean(density_scores)
        avg_processing_time = np.mean(processing_times)
        success_rate = success_count / len(test_dataset)
        
        # Normalize compression ratio (log scale, capped at 100x)
        normalized_compression = min(np.log10(max(avg_compression, 1.0)) / 2.0, 1.0)
        
        # Processing speed score (inverse of time, normalized)
        speed_score = min(1.0 / max(avg_processing_time, 0.001), 10.0) / 10.0
        
        # Calculate overall score with balanced weights
        overall_score = (
            0.25 * normalized_compression +      # Compression efficiency
            0.30 * avg_semantic +               # Semantic preservation
            0.25 * avg_cross_modal +            # Cross-modal preservation
            0.10 * avg_density +                # Information density
            0.10 * speed_score                  # Processing speed
        ) * success_rate  # Penalize failures
        
        return {
            "compression_ratio": float(avg_compression),
            "semantic_preservation": float(avg_semantic),
            "cross_modal_preservation": float(avg_cross_modal),
            "processing_speed": float(speed_score),
            "information_density": float(avg_density),
            "overall_score": float(overall_score),
            "success_rate": float(success_rate),
            "avg_processing_time": float(avg_processing_time),
            "samples_processed": int(success_count)
        }
        
    except Exception as e:
        print(f"Evaluation failed: {str(e)}")
        print(traceback.format_exc())
        return {
            "compression_ratio": 0.0,
            "semantic_preservation": 0.0,
            "cross_modal_preservation": 0.0,
            "processing_speed": 0.0,
            "information_density": 0.0,
            "overall_score": 0.0,
            "error": str(e)
        }

# Cascade evaluation stages
def evaluate_stage1(program_path):
    """Quick validation stage - check if program runs and produces output."""
    try:
        spec = importlib.util.spec_from_file_location("program", program_path)
        program = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(program)
        
        if not hasattr(program, "process_multimodal_data"):
            return {"runs_successfully": 0.0, "error": "Missing function"}
        
        # Quick test with minimal data
        result = run_with_timeout(
            program.process_multimodal_data,
            kwargs={'text': "test", 'image': np.random.rand(8, 8, 3)},
            timeout_seconds=5
        )
        
        if isinstance(result, np.ndarray) and result.size > 0:
            return {"runs_successfully": 1.0, "output_size": int(result.size)}
        else:
            return {"runs_successfully": 0.0, "error": "Invalid output"}
            
    except Exception as e:
        return {"runs_successfully": 0.0, "error": str(e)}

def evaluate_stage2(program_path):
    """Full evaluation with complete metrics."""
    return evaluate(program_path)
