# Configuration for Multimodal Data Condensation Evolution
max_iterations: 15
checkpoint_interval: 5
log_level: "INFO"

# LLM configuration
llm:
  primary_model: "gemini-2.0-flash-lite"
  primary_model_weight: 0.8
  secondary_model: "gemini-2.0-flash"
  secondary_model_weight: 0.2
  api_base: "https://generativelanguage.googleapis.com/v1beta/openai/"
  api_key: "AIzaSyAr-5UzASN7jNott9_4TxUvXPa_gTam1_o"
  temperature: 0.7
  top_p: 0.95
  max_tokens: 4096

# Prompt configuration
prompt:
  system_message: "You are an expert programmer specializing in multimodal data processing and compression algorithms. Your task is to improve a Python algorithm that condenses multimodal data (text, images, video, audio) into compact representations. Focus on improving compression ratios, semantic preservation, and cross-modal relationships. Make targeted improvements to the code within EVOLVE-BLOCK sections."
  num_top_programs: 3
  num_diverse_programs: 2
  use_template_stochasticity: true

# Database configuration
database:
  population_size: 50
  archive_size: 20
  num_islands: 3
  elite_selection_ratio: 0.2
  exploitation_ratio: 0.7

# Evaluator configuration
evaluator:
  timeout: 60
  cascade_evaluation: true
  cascade_thresholds: [0.5, 0.75]
  parallel_evaluations: 3
  use_llm_feedback: false

# Evolution settings
diff_based_evolution: true
allow_full_rewrites: false
